plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
    id 'com.google.gms.google-services'
    id 'com.google.firebase.crashlytics'
    id 'com.sensorsdata.analytics.android' //神策
}

def COMMIT_HASH = "git rev-parse --short HEAD".execute().text.replaceAll("\\R", "")
def USER_APP_NAME = "YesssCredit"
def SALES_APP_NAME = "Yesss Mitra"

android {
    namespace 'com.intl'
    compileSdk rootProject.ext.compileSdkVersion

    defaultConfig {
        //applicationId "id.consumerfinance.sales"
        minSdk rootProject.ext.minSdkVersion
        targetSdk rootProject.ext.targetSdkVersion
        //versionCode rootProject.ext.appVersionCode
        //versionName rootProject.ext.appVersionName
        buildConfigField("String", "BUILD_TIME", "\"" + rootProject.ext.buildTime + "\"")
        buildConfigField("String", "COMMIT_HASH", "\"" + "${COMMIT_HASH}" + "\"")
        resValue "string", "dl_host", "h5.yessscredit.id"
        multiDexEnabled true
        multiDexKeepProguard file('multidex.pro')
        ndk {
            abiFilters "armeabi-v7a", "arm64-v8a"
        }
    }
    dataBinding {
        enabled true
    }
    signingConfigs {
        String signStoreFile = "${rootProject.projectDir}/tool/release.jks"
        String signKeyAlias = "key0"
        String signKeyPassword = "5b!#315D"
        String signStorePassword = "5b!#315D"
        debug {
        }
        release {
            storeFile file(signStoreFile)
            keyAlias signKeyAlias
            keyPassword signKeyPassword
            storePassword signStorePassword
        }
    }
    buildTypes {
        release {
            debuggable false
            signingConfig signingConfigs.release
            minifyEnabled false
            shrinkResources false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            zipAlignEnabled false
            manifestPlaceholders.app_name_suffix = ""
            manifestPlaceholders.appIcon = "@mipmap/ic_launcher"
            manifestPlaceholders.appIconRound = "@mipmap/ic_launcher_round"
            manifestPlaceholders.network_security_config = "@xml/network_security_config_release"
        }
        //预发布包，供测试使用
        stage {
            debuggable true
            signingConfig signingConfigs.release
            minifyEnabled false
            shrinkResources false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            zipAlignEnabled false
            matchingFallbacks = ['release']
            manifestPlaceholders.app_name_suffix = "-${COMMIT_HASH}"
            manifestPlaceholders.appIcon = "@mipmap/ic_launcher_dev"
            manifestPlaceholders.appIconRound = "@mipmap/ic_launcher_round_dev"
            manifestPlaceholders.network_security_config = "@xml/network_security_config_dev"
        }
        debug {
            signingConfig signingConfigs.release
            manifestPlaceholders.app_name_suffix = "-${COMMIT_HASH}"
            manifestPlaceholders.appIcon = "@mipmap/ic_launcher_dev"
            manifestPlaceholders.appIconRound = "@mipmap/ic_launcher_round_dev"
            manifestPlaceholders.network_security_config = "@xml/network_security_config_dev"
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }

    flavorDimensions "flavorDimensions"
    productFlavors {
        // user start
        user {
            dimension "flavorDimensions"
            applicationId "id.yessscredit.kredit.pinjaman.cicilan"
            versionCode 10102
            versionName "1.1.2"
            resValue "string", "app_name", "${USER_APP_NAME}"
            resValue "string", "dl_path", "/user/dl"
            resValue "string", "dl_custom_scheme", "ycuser"
            manifestPlaceholders.app_name = "${USER_APP_NAME}"
        }
        // user end

        // sales start
        sales {
            dimension "flavorDimensions"
            applicationId "id.yessscredit.mirta"
            versionCode 10300
            versionName "1.4.0"
            resValue "string", "app_name", "${SALES_APP_NAME}"
            resValue "string", "dl_path", "/sales/dl"
            resValue "string", "dl_custom_scheme", "ycsales"
            manifestPlaceholders.app_name = "${SALES_APP_NAME}"
        }
        // sales end
    }
    lint {
        abortOnError false
    }

    // 重命名打包产物
    // todo: 仅对apk有效，对aab无效
    android.applicationVariants.all { variant ->
        variant.outputs.each { output ->
            output.outputFileName = "${variant.name}-${variant.versionName}-${variant.versionCode}-${COMMIT_HASH}.apk"
        }
    }
}

dependencies {
    api project(':lib_common')
    api project(':lib_face')
    api('com.flyco.tablayout:FlycoTabLayout_Lib:2.1.2') {
        exclude group: 'com.android.support'
    }

    // firebase start
    // Import the Firebase BoM
    implementation platform('com.google.firebase:firebase-bom:32.8.1')
    // Add the dependencies for Firebase products you want to use
    // When using the BoM, don't specify versions in Firebase dependencies
    implementation 'com.google.firebase:firebase-analytics'
    implementation 'com.google.firebase:firebase-messaging-ktx'
    implementation 'com.google.firebase:firebase-crashlytics-ktx'
    // Add the dependencies for any other desired Firebase products
    // https://firebase.google.com/docs/android/setup#available-libraries
    // firebase end

    implementation "androidx.lifecycle:lifecycle-extensions:2.2.0"
    implementation 'com.sensorsdata.analytics.android:SensorsAnalyticsSDK:6.8.3'//神策

    implementation "androidx.work:work-runtime:2.8.0"  // worker
    implementation "androidx.work:work-runtime-ktx:2.8.1"
}

task getReleaseConfig {
    doLast {
        def signingConfig = android.signingConfigs.getByName("release")
        String keyAlias = signingConfig.getKeyAlias()
        String storePwd = signingConfig.getStorePassword()
        String keyPwd = signingConfig.getKeyPassword()
        String storePath = signingConfig.getStoreFile().getAbsolutePath()

        println android.getSdkDirectory().getAbsolutePath()
        println android.buildToolsVersion
        println storePath
        println storePwd
        println keyAlias
        println keyPwd
        println rootProject.ext.appVersionName
    }
}
