<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />

    <!--user和sales需要不同的权限，所以放在各自的AndroidManifest里-->
    <!--不同的buildType也需要不同的权限，所以放在各自的AndroidManifest里-->

    <application
        android:name="com.intl.base.BaseApplication"
        android:allowBackup="false"
        android:icon="${appIcon}"
        android:label="${app_name}${app_name_suffix}"
        android:networkSecurityConfig="${network_security_config}"
        android:roundIcon="${appIconRound}"
        android:theme="@style/Theme.App">
        <activity
            android:name="com.intl.bu.activity.SplashActivity"
            android:exported="true"
            android:theme="@style/Theme.Splash">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <activity
            android:name="com.intl.bu.activity.MainActivity"
            android:exported="true"
            android:launchMode="singleTask">
            <!--从短信唤起app，未安装则跳H5-->
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="https" />
                <data android:host="@string/dl_host" />
                <data android:pathPrefix="@string/dl_path" />
            </intent-filter>

            <!--从js精确唤起app-->
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="@string/dl_custom_scheme" />
            </intent-filter>
        </activity>
        <activity android:name="com.intl.bu.activity.DevToolActivity" />
        <activity
            android:name="com.intl.bu.activity.PermissionShimActivity"
            android:theme="@style/Theme.Transparent" />
        <activity
            android:name="com.intl.bu.activity.ContactShimActivity"
            android:theme="@style/Theme.Transparent" />

        <service
            android:name="com.intl.bu.service.MyFirebaseMessagingService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <receiver
            android:name=".base.OtpCodeReceiver"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="com.whatsapp.otp.OTP_RETRIEVED" />
            </intent-filter>
        </receiver>

    </application>

</manifest>
