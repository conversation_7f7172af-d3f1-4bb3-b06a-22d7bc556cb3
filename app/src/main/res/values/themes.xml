<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.App" parent="Theme.MaterialComponents.Light.DarkActionBar">
        <!-- Status bar color. -->
        <item name="android:statusBarColor">@color/white</item>
        <!-- Customize your theme here. -->
        <item name="android:windowAnimationStyle">@style/ActivityAnimation</item>
    </style>

    <style name="Theme.Splash" parent="Theme.App">
        <!--去掉android12+默认的启动图，防止和我们自定义的SplashActivity重复-->
        <item name="android:windowIsTranslucent">true</item>
    </style>

    <style name="Theme.Transparent" parent="Theme.MaterialComponents.Light.NoActionBar">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
    </style>

</resources>