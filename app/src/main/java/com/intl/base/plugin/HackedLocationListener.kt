package com.intl.base.plugin

import android.location.Location
import android.location.LocationListener
import android.os.Bundle

/**
 * hack: 低版本安卓上（例如8.1.0），报AbstractMethodError，需要手动实现这几个方法
 */
class HackedLocationListener : LocationListener {
    override fun onProviderDisabled(provider: String) {
        //super.onProviderDisabled(provider) //不能调super，因为没实现
    }

    override fun onLocationChanged(location: Location) {
        //super.onProviderDisabled(provider) //不能调super，因为没实现
    }

    override fun onStatusChanged(provider: String, status: Int, extras: Bundle) {
        //super.onProviderDisabled(provider) //不能调super，因为没实现
    }
}