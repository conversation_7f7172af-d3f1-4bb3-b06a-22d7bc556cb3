package com.intl.base.plugin

import com.intl.BuildConfig
import com.intl.base.Util
import com.xinye.xmp.bridge.XMPPlugin
import com.xinye.xmp.ui.XMPFragment
import com.xinye.xmp.ui.XMPWebView
import org.json.JSONObject

class PluginGetAppInfo : XMPPlugin() {
    override fun exec(f: XMPFragment?, w: XMPWebView?, data: JSONObject?, cbk: Callback?) {
        Util.log("js call $name")

        data class Out(val version: String)

        val vName = BuildConfig.VERSION_NAME
        cbk?.complete(Util.toJSONObject(Out(version = vName)))
    }

    override fun getName(): String {
        return "get_app_info"
    }
}