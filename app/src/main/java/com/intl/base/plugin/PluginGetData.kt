package com.intl.base.plugin

import com.intl.base.Constant
import com.intl.base.Util
import com.intl.lib_common.util.PrefUtil
import com.xinye.xmp.bridge.XMPPlugin
import com.xinye.xmp.ui.XMPFragment
import com.xinye.xmp.ui.XMPWebView
import org.json.JSONObject

class PluginGetData : XMPPlugin() {

    override fun exec(f: XMPFragment?, w: XMPWebView?, data: JSONObject?, cbk: Callback?) {
        Util.log("js call $name")

        data class In(val key: String?)
        data class Out(val value: String?)

        val p = Util.toObj(data!!, In::class.java)
        if (p.key.isNullOrEmpty()) {
            // 当key传空，总是返回null
            cbk?.complete(Util.toJSONObject(Out(value = null)))
        } else {
            // 如实返回，返回null表示key不存在或者值为null
            val value = PrefUtil.getString(f!!.requireContext(), "${Constant.SP_KEY_PREFIX_FOR_H5}${p.key}")
            cbk?.complete(Util.toJSONObject(Out(value = value)))
        }
    }

    override fun getName(): String {
        return "get_data"
    }

}