package com.intl.base.plugin

import androidx.lifecycle.lifecycleScope
import com.intl.base.SensorsDataUtil
import com.intl.base.Util
import com.xinye.xmp.bridge.XMPPlugin
import com.xinye.xmp.ui.XMPFragment
import com.xinye.xmp.ui.XMPWebView
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import okhttp3.Dns
import org.json.JSONObject
import java.net.InetAddress

/**
 * 用okhttp测api的连通性
 */
class PluginTestDns : XMPPlugin() {
    override fun exec(f: XMPFragment?, w: XMPWebView?, data: JSONObject?, cbk: Callback?) {
        Util.log("js call $name")

        try {
            val p = Util.toObj(data!!, In::class.java)
            val method = p.method
            val host = p.host

            f?.context?.let {
                f.lifecycleScope.launch {
                    try {
                        withContext(Dispatchers.IO) {
                            try {
                                if (method == Method.OKHTTP.value) {
                                    Dns.SYSTEM.lookup(host)
                                } else {
                                    InetAddress.getAllByName(host)
                                }
                                trackTestResult(method, host, "ok")
                                callJs(cbk, method, host, "ok")
                            } catch (e: Exception) {
                                trackTestResult(method, host, e.message ?: "")
                                callJs(cbk, method, host, e.message ?: "")
                            }
                        }
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private suspend fun callJs(cbk: Callback?, method: String, host: String, message: String) {
        withContext(Dispatchers.Main) {
            cbk?.complete(Util.toJSONObject(Out(method, host, message))) //must call js in main thread
        }
    }

    private fun trackTestResult(method: String, host: String, message: String = "") {
        try {
            val map = HashMap<String, Any>()
            map["method"] = method
            map["host"] = host
            map["message"] = message
            SensorsDataUtil.appAuth("debug", "debug_test_dns", "测试DNS", map)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override fun getName(): String {
        return "test_dns"
    }

    private enum class Method(val value: String) {
        OKHTTP("okhttp"), SYSTEM("system")
    }

    companion object {
        data class In(val method: String, val host: String)
        data class Out(val method: String, val host: String, val message: String)
    }
}