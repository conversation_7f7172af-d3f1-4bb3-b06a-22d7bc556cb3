package com.intl.base.plugin

import com.intl.base.ScanProxyFragment
import com.intl.base.Util
import com.xinye.xmp.bridge.XMPPlugin
import com.xinye.xmp.ui.XMPFragment
import com.xinye.xmp.ui.XMPWebView
import org.json.JSONObject

class PluginScan : XMPPlugin() {
    override fun exec(f: XMPFragment?, w: XMPWebView?, data: JSONObject?, cbk: Callback?) {
        Util.log("js call $name")

        ScanProxyFragment.start(f?.activity) {
            Util.log("get qr result: $it")
            data class Out(val result: String)
            cbk?.complete(Util.toJSONObject(Out(it ?: "")))
        }
    }

    override fun getName(): String {
        return "scan"
    }
}