package com.intl.base.plugin

import android.util.Log
import com.google.android.gms.tasks.OnCompleteListener
import com.google.android.gms.tasks.OnFailureListener
import com.google.firebase.messaging.FirebaseMessaging
import com.intl.base.Util
import com.xinye.xmp.bridge.XMPPlugin
import com.xinye.xmp.ui.XMPFragment
import com.xinye.xmp.ui.XMPWebView
import org.json.JSONObject
import java.lang.Exception

class PluginGetPushToken : XMPPlugin() {
    override fun exec(f: XMPFragment?, w: XMPWebView?, data: JSONObject?, cbk: Callback?) {
        Util.log("js call $name")
        data class Out(val token: String)

        FirebaseMessaging.getInstance().token.addOnCompleteListener { task ->
            if (task.isSuccessful) {
                val token = task.result
                Log.d("mayqx", "给js返回推送token：$token")
                cbk?.complete(Util.toJSONObject(Out(token = token)))
            } else {
                Log.d("mayqx", "拿不到推送token")
                cbk?.complete(Util.toJSONObject(Out(token = "")))
            }
        }.addOnFailureListener {
            Log.d("mayqx", "获取推送token异常, e=$it")
            cbk?.complete(Util.toJSONObject(Out(token = "")))
        }
    }

    override fun getName(): String {
        return "get_push_token"
    }
}