package com.intl.base.plugin

import com.intl.base.Util
import com.intl.lib_common.util.AdaRisk
import com.xinye.xmp.bridge.XMPPlugin
import com.xinye.xmp.ui.XMPFragment
import com.xinye.xmp.ui.XMPWebView
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.json.JSONObject

class PluginGetAdaDeviceRisk : XMPPlugin() {
    override fun exec(f: XMPFragment?, w: XMPWebView?, data: JSONObject?, cbk: Callback?) {
        Util.log("js call $name")

        CoroutineScope(Dispatchers.IO).launch {
            data class Out(val root: <PERSON><PERSON><PERSON>, val emulator: Boolean)

            val root = AdaRisk.isRootSlow(f!!.requireContext())
            val emulator = AdaRisk.isEmulator()

            withContext(Dispatchers.Main) {
                cbk?.complete(Util.toJSONObject(Out(root = root, emulator = emulator)))
            }
        }
    }

    override fun getName(): String {
        return "get_ada_device_risk"
    }
}