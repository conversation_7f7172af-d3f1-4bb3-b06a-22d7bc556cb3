package com.intl.base.plugin

import com.intl.base.ContactData
import com.intl.base.Util
import com.intl.bu.activity.ContactShimActivity
import com.xinye.xmp.bridge.XMPPlugin
import com.xinye.xmp.ui.XMPFragment
import com.xinye.xmp.ui.XMPWebView
import org.json.JSONObject

class PluginPickContact : XMPPlugin() {

    override fun exec(f: XMPFragment?, w: XMPWebView?, data: JSONObject?, cbk: Callback?) {
        try {
            Util.log("js call $name")

            ContactShimActivity.start(f!!.requireContext())
            ContactShimActivity.callback = { it ->
                cbk?.complete(Util.toJSONObject(it))
            }
        } catch (e: Exception) {
            cbk?.complete(Util.toJSONObject(ContactData("", "")))
            e.printStackTrace()
        }
    }

    override fun getName(): String {
        return "pick_contact"
    }

}