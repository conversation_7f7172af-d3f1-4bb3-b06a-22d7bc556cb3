package com.intl.base.plugin

import android.Manifest
import android.annotation.SuppressLint
import android.app.Activity
import android.app.AlertDialog
import android.os.Build
import android.util.Log
import androidx.core.app.ActivityCompat
import com.intl.R
import com.intl.base.DataType
import com.intl.base.NotificationUtil
import com.intl.base.SpecialPermissionUtil
import com.intl.base.Util
import com.xinye.xmp.bridge.XMPPlugin
import com.xinye.xmp.permission.PermissionTerminator
import com.xinye.xmp.permission.PermissionUserCallback
import com.xinye.xmp.ui.XMPFragment
import com.xinye.xmp.ui.XMPWebView
import org.json.JSONObject

class PluginRequestPermission : XMPPlugin() {
    override fun exec(f: XMPFragment?, w: XMPWebView?, data: JSONObject?, cbk: Callback?) {
        Util.log("js调安卓, $name")

        data class In(val type: String)
        data class Out(val granted: Boolean)

        val p = Util.toObj(data!!, In::class.java)

        Log.d("mayqx", "权限=${p.type}")
        val enum = DataType.get(p.type)

        if (enum == null) {
            // 申请约定之外的权限，视为未授权
            cbk?.complete(Util.toJSONObject(Out(false)))
        }

        if (enum == DataType.NOTIFICATION) {
            // xmp暂时不支持通知权限，所以特殊处理
            if (NotificationUtil.isNotificationEnabled(f?.requireContext())) {
                cbk?.complete(Util.toJSONObject(Out(true)))
            } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                // 高版本可原地授权
                // todo: 让xmp支持这个权限，更优雅的获取授权结果
                ActivityCompat.requestPermissions(
                    f!!.requireActivity(),
                    arrayOf(Manifest.permission.POST_NOTIFICATIONS),
                    123,
                )
                cbk?.complete(Util.toJSONObject(Out(false)))
            } else {
                // 低版本要跳设置页
                AlertDialog.Builder(f!!.requireContext()).setMessage(f.requireContext().getString(R.string.enable_notification))
                    .setPositiveButton("OK") { dlg, _ ->
                        run {
                            cbk?.complete(Util.toJSONObject(Out(false)))//用户点击后回调js，从设置回来会触发js的onShow
                            NotificationUtil.goNotificationSetting(f.requireActivity(), 123)
                            dlg.dismiss()
                        }
                    }.setCancelable(false).show()
                return //等用户点击弹窗按钮后回调js
            }
        } else if (enum == DataType.APP_USAGE) {
            // appUsage是特殊权限，要跳系统设置页打开
            if (enum.permission != null) {
                SpecialPermissionUtil.requestPermission(f!!.requireContext(), enum.permission) { granted ->
                    cbk?.complete(Util.toJSONObject(Out(granted)))
                }
            } else {
                // api<23无需权限
                cbk?.complete(Util.toJSONObject(Out(true)))
            }
        } else if (enum == DataType.DETECT_SCREENSHOT) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                PermissionTerminator.requestPermission(f!!.activity, arrayOf(enum.permission), object : PermissionUserCallback() {
                    @SuppressLint("MissingPermission")
                    override fun permissionGranted(p0: Activity?) {
                        Log.d("mayqx", "${p.type}, 授权成功")
                        cbk?.complete(Util.toJSONObject(Out(true)))
                    }

                    override fun permissionRefused(p0: Activity?) {
                        Log.d("mayqx", "${p.type}, 授权失败")
                        cbk?.complete(Util.toJSONObject(Out(false)))
                    }
                })
            } else {
                cbk?.complete(Util.toJSONObject(Out(true)))
            }
        } else {
            if (enum == DataType.LOCATION || enum == DataType.WIFI_LIST) {
                if (!Util.isLocationEnabled(f!!.requireContext())) {
                    AlertDialog.Builder(f.requireContext()).setMessage(f.requireContext().getString(R.string.enable_location_service))
                        .setPositiveButton("OK") { dlg, _ ->
                            run {
                                cbk?.complete(Util.toJSONObject(Out(false)))//用户点击后回调js，从设置回来会触发js的onShow
                                Util.goLocationSetting(f.requireContext())
                                dlg.dismiss()
                            }
                        }.setCancelable(false).show()

                    return //提前结束，不继续申请权限
                }
            }

            if (enum?.permission == null) {
                // 不需要权限，视为已授权
                cbk?.complete(Util.toJSONObject(Out(true)))
            }

            PermissionTerminator.requestPermission(f!!.activity, arrayOf(enum!!.permission), object : PermissionUserCallback() {
                @SuppressLint("MissingPermission")
                override fun permissionGranted(p0: Activity?) {
                    Log.d("mayqx", "${p.type}, 授权成功")
                    cbk?.complete(Util.toJSONObject(Out(true)))
                }

                override fun permissionRefused(p0: Activity?) {
                    Log.d("mayqx", "${p.type}, 授权失败")
                    cbk?.complete(Util.toJSONObject(Out(false)))
                }
            })
        }
    }

    override fun getName(): String {
        return "request_permission"
    }
}