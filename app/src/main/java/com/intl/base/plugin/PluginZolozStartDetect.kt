package com.intl.base.plugin

import com.intl.base.Util
import com.intl.lib_common.MyZolozCallback
import com.intl.lib_common.Zoloz
import com.xinye.xmp.bridge.XMPPlugin
import com.xinye.xmp.ui.XMPFragment
import com.xinye.xmp.ui.XMPWebView
import org.json.JSONObject

class PluginZolozStartDetect : XMPPlugin() {
    override fun exec(f: XMPFragment?, w: XMPWebView?, data: JSONObject?, cbk: Callback?) {
        Util.log("js call $name")

        data class In(val rsa: String, val cfg: String)
        data class Out(val success: Boolean)

        val p = Util.toObj(data!!, In::class.java)

        val context = f!!.requireContext()
        Zoloz.startDetection(context, p.rsa, p.cfg, object : MyZolozCallback {
            override fun onComplete() {
                cbk?.complete(Util.toJSONObject(Out(true)))
            }

            override fun onInterrupt() {
                cbk?.complete(Util.toJSONObject(Out(false)))
            }
        })
    }

    override fun getName(): String {
        return "zoloz_start_detect"
    }
}