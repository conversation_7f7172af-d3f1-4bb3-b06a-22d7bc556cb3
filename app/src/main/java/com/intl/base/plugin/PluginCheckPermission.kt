package com.intl.base.plugin

import android.os.Build
import android.util.Log
import com.intl.base.AppUsageUtil
import com.intl.base.DataType
import com.intl.base.NotificationUtil
import com.intl.base.Util
import com.xinye.xmp.bridge.XMPPlugin
import com.xinye.xmp.permission.PermissionTerminator
import com.xinye.xmp.ui.XMPFragment
import com.xinye.xmp.ui.XMPWebView
import org.json.JSONObject

class PluginCheckPermission : XMPPlugin() {
    override fun exec(f: XMPFragment?, w: XMPWebView?, data: JSONObject?, cbk: Callback?) {
        Util.log("js调安卓, $name")

        data class In(val type: String)
        data class Out(val granted: Boolean)

        val p = Util.toObj(data!!, In::class.java)

        Log.d("mayqx", "权限=${p.type}")
        val enum = DataType.get(p.type)

        if (enum == null) {
            // 检查约定之外的权限，视为未授权
            cbk?.complete(Util.toJSONObject(Out(false)))
        }

        if (enum == DataType.NOTIFICATION) {
            // xmp暂时不支持通知权限，所以特殊处理
            val granted = NotificationUtil.isNotificationEnabled(f?.requireContext())
            cbk?.complete(Util.toJSONObject(Out(granted)))
        } else if (enum == DataType.APP_USAGE) {
            // xmp暂时不支持appUsage权限，所以特殊处理
            if (AppUsageUtil.isAppUsageEnabled(f!!.requireContext())) {
                cbk?.complete(Util.toJSONObject(Out(true)))
            } else {
                cbk?.complete(Util.toJSONObject(Out(false)))
            }
        } else if (enum == DataType.DETECT_SCREENSHOT) {
            // (,9]: 无需权限
            // [10, 13]: 读取存储权限
            // [14,]: 如果用官方方案则无需权限（官方方案只能由Activity监听，然后传给当前可见的Fragment，麻烦，所以继续用老方案做，需要读取存储权限）
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                val granted = PermissionTerminator.checkPermission(enum.permission)
                cbk?.complete(Util.toJSONObject(Out(granted)))
            } else {
                cbk?.complete(Util.toJSONObject(Out(true)))
            }
        } else {
            if (enum?.permission == null) {
                // 无对应权限，视为已授权
                cbk?.complete(Util.toJSONObject(Out(true)))
            }

            val granted = PermissionTerminator.checkPermission(enum!!.permission)

            if (enum == DataType.LOCATION || enum == DataType.WIFI_LIST) {
                // 这2个场景特殊，还要位置服务
                val locationServiceEnabled = Util.isLocationEnabled(f!!.requireContext())
                cbk?.complete(Util.toJSONObject(Out(granted && locationServiceEnabled)))
            } else {
                cbk?.complete(Util.toJSONObject(Out(granted)))
            }
        }
    }

    override fun getName(): String {
        return "check_permission"
    }
}