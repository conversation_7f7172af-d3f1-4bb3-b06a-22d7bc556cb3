package com.intl.base.plugin

import android.content.Intent
import android.net.Uri
import com.intl.base.Util
import com.xinye.xmp.bridge.XMPPlugin
import com.xinye.xmp.ui.XMPFragment
import com.xinye.xmp.ui.XMPWebView
import org.json.JSONObject


class PluginOpenUrlInBrowser : XMPPlugin() {
    override fun exec(f: XMPFragment?, w: XMPWebView?, data: JSONObject?, cbk: Callback?) {
        Util.log("js call $name")
        data class In(val url: String)
        data class Out(val success: Boolean)

        val p = Util.toObj(data!!, In::class.java)

        try {
            val i = Intent(Intent.ACTION_VIEW)
            i.setData(Uri.parse(p.url))
            f!!.requireActivity().startActivity(i)
        } catch (e: Exception) {
            e.printStackTrace()
            cbk?.complete(Util.toJSONObject(Out(success = false)))
        }

        cbk?.complete(Util.toJSONObject(Out(success = true)))
    }

    override fun getName(): String {
        return "open_url_in_browser"
    }

}