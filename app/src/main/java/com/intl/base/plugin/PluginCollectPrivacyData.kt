package com.intl.base.plugin

import com.intl.base.DataType
import com.intl.base.Util
import com.intl.bu.model.api.ApiUploadPrivacyData
import com.intl.lib_common.util.DataCollectionSdkUtil
import com.xinye.xmp.bridge.XMPPlugin
import com.xinye.xmp.ui.XMPFragment
import com.xinye.xmp.ui.XMPWebView
import org.json.JSONObject
import java.io.IOException

class PluginCollectPrivacyData : XMPPlugin() {
    override fun exec(f: XMPFragment?, w: XMPWebView?, data: JSONObject?, cbk: Callback?) {
        Util.log("js调安卓, $name, data=$data")

        data class In(val type: String, val url: String, val headers: Map<String, String>)
        data class Out(val success: Boolean)

        val p = Util.toObj(data!!, In::class.java)
        val enum = DataType.get(p.type)

        if (enum == null) {
            // 采集约定之外的数据，返回失败
            cbk?.complete(Util.toJSONObject(Out(false)))
        }

        DataCollectionSdkUtil.collect(enum!!.acType) { map, s, resultCallback ->
            Util.log("数据采集回调, map=$map")
            Util.post(
                ApiUploadPrivacyData(p.url),
                p.headers,
                ApiUploadPrivacyData.decodeSdkData(map),
                object : Util.TypeCallback<ApiUploadPrivacyData.Out> {
                    override fun onResponse(httpSuccess: Boolean, responseBody: ApiUploadPrivacyData.Out?) {
                        resultCallback?.uploadSuccess("success")
                    }

                    override fun onFailure(e: IOException) {
                        resultCallback?.uploadFail(e)
                    }

                }
            )
        }

        cbk?.complete(Util.toJSONObject(Out(success = true)))
    }

    override fun getName(): String {
        return "collect_privacy_data"
    }
}