package com.intl.base.plugin

import android.view.WindowManager
import com.intl.base.Constant
import com.intl.base.Util
import com.intl.lib_common.util.PrefUtil
import com.xinye.xmp.bridge.XMPPlugin
import com.xinye.xmp.ui.XMPFragment
import com.xinye.xmp.ui.XMPWebView
import org.json.JSONObject

/**
 * 禁止或允许截屏
 */
class PluginSetScreenshot : XMPPlugin() {

    override fun exec(f: XMPFragment?, w: XMPWebView?, data: JSONObject?, cbk: Callback?) {
        Util.log("js call $name")

        data class In(val allow: Boolean?)

        val p = Util.toObj(data!!, In::class.java)
        if (p.allow == true) {
            f?.activity?.window?.clearFlags(WindowManager.LayoutParams.FLAG_SECURE)
        } else {
            f?.activity?.window?.setFlags(
                WindowManager.LayoutParams.FLAG_SECURE, WindowManager.LayoutParams.FLAG_SECURE
            )
        }

        cbk?.complete(null)
    }

    override fun getName(): String {
        return "set_screenshot"
    }

}