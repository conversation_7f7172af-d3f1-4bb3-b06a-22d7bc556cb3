package com.intl.base.plugin

import com.intl.base.Util
import com.intl.bu.model.api.ApiUploadTongDun
import com.intl.lib_common.util.TongDunUtil
import com.xinye.xmp.bridge.XMPPlugin
import com.xinye.xmp.ui.XMPFragment
import com.xinye.xmp.ui.XMPWebView
import org.json.JSONObject
import java.io.IOException

class PluginCollectTongdun : XMPPlugin() {
    override fun exec(f: XMPFragment?, w: XMPWebView?, data: JSONObject?, cbk: Callback?) {
        Util.log("js call $name")

        data class In(val bizType: String, val url: String, val headers: Map<String, String>)
        data class Out(val success: Boolean)

        val p = Util.toObj(data!!, In::class.java)

        TongDunUtil.collect { blackBox ->
            Util.post(
                ApiUploadTongDun(p.url),
                p.headers,
                ApiUploadTongDun.In(blackBox, p.bizType, ""),
                object : Util.TypeCallback<ApiUploadTongDun.Out> {
                    override fun onResponse(httpSuccess: Boolean, responseBody: ApiUploadTongDun.Out?) {
                        //
                    }

                    override fun onFailure(e: IOException) {
                        //
                    }

                }
            )
        }
        cbk?.complete(Util.toJSONObject(Out(true)))
    }

    override fun getName(): String {
        return "collect_tongdun"
    }
}