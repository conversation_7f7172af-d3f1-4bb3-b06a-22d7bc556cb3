package com.intl.base.plugin

import com.intl.base.SensorsDataUtil
import com.intl.base.Util
import com.xinye.xmp.bridge.XMPPlugin
import com.xinye.xmp.ui.XMPFragment
import com.xinye.xmp.ui.XMPWebView
import org.json.JSONObject

/**
 * xmp会调这个插件，记录H5页面的加载时长和报错
 */
class PluginReport : XMPPlugin() {
    override fun exec(f: XMPFragment?, w: XMPWebView?, data: JSONObject?, cbk: Callback?) {
        try {
            Util.log("xmp call $name")

            val map = HashMap<String, Any>()
            data?.keys()?.forEach {
                map[it] = data.get(it)
            }

            SensorsDataUtil.webViewEvent(map)

            cbk?.complete(null)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override fun getName(): String {
        return "report"
    }
}