package com.intl.base.plugin

import com.intl.base.Constant
import com.intl.base.Util
import com.intl.lib_common.util.PrefUtil
import com.xinye.xmp.bridge.XMPPlugin
import com.xinye.xmp.ui.XMPFragment
import com.xinye.xmp.ui.XMPWebView
import org.json.JSONObject

/**
 * H5在ios上遇到一个问题，在WebView1写localStorage，在WebView2读取不到；
 * 没找到好的解决方案；
 * 所以我们写这个插件，借助原生存取数据；
 */
class PluginRemoveData : XMPPlugin() {

    override fun exec(f: XMPFragment?, w: XMPWebView?, data: JSONObject?, cbk: Callback?) {
        Util.log("js call $name")

        data class In(val key: String?)
        data class Out(val success: Boolean)

        val p = Util.toObj(data!!, In::class.java)
        if (p.key.isNullOrEmpty()) {
            // key不能为空
            cbk?.complete(Util.toJSONObject(Out(success = false)))
        } else {
            PrefUtil.removeString(f!!.requireContext(), "${Constant.SP_KEY_PREFIX_FOR_H5}${p.key}")
            cbk?.complete(Util.toJSONObject(Out(success = true)))
        }
    }

    override fun getName(): String {
        return "remove_data"
    }

}