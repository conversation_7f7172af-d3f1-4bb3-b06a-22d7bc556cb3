package com.intl.base.plugin

import android.content.Intent
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.util.Base64
import androidx.core.content.FileProvider
import com.intl.base.Util
import com.xinye.xmp.bridge.XMPPlugin
import com.xinye.xmp.ui.XMPFragment
import com.xinye.xmp.ui.XMPWebView
import org.json.JSONObject
import java.io.File
import java.io.FileOutputStream

class PluginShareImage : XMPPlugin() {
    override fun exec(f: XMPFragment?, w: XMPWebView?, data: JSONObject?, cbk: Callback?) {
        Util.log("js call $name")
        data class In(val base64: String)
        data class Out(val success: Boolean)

        val p = Util.toObj(data!!, In::class.java)

        try {
            val context = f!!.requireContext()

            // 1. 解码 Base64 为 Bitmap
            val pureBase64 = p.base64.substringAfter(",") // 去掉前缀 data:image/png;base64,
            val decodedBytes = Base64.decode(pureBase64, Base64.DEFAULT)
            val bitmap = BitmapFactory.decodeByteArray(decodedBytes, 0, decodedBytes.size)

            // 2. 写入临时文件
            val file = File(context.cacheDir, "shared_image.png")
            val fos = FileOutputStream(file)
            bitmap.compress(Bitmap.CompressFormat.PNG, 100, fos)
            fos.flush()
            fos.close()

            // 3. 获取 content URI
            val uri = FileProvider.getUriForFile(
                context,
                "${context.packageName}.fileprovider",
                file
            )

            // 4. 构建分享 Intent
            val shareIntent = Intent(Intent.ACTION_SEND).apply {
                type = "image/png"
                putExtra(Intent.EXTRA_STREAM, uri)
                addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            }

            // 5. 启动分享面板
            context.startActivity(Intent.createChooser(shareIntent, "Share"))

            cbk?.complete(Util.toJSONObject(Out(success = true)))
        } catch (e: Exception) {
            e.printStackTrace()
            cbk?.complete(Util.toJSONObject(Out(success = false)))
        }
    }

    override fun getName(): String {
        return "share_image"
    }

}