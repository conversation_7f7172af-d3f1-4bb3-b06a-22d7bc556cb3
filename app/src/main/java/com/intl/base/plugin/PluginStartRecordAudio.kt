package com.intl.base.plugin

import com.intl.base.AudioRecordUtil
import com.intl.base.Util
import com.intl.bu.model.api.JsUploadAudioParam
import com.xinye.xmp.bridge.XMPPlugin
import com.xinye.xmp.ui.XMPFragment
import com.xinye.xmp.ui.XMPWebView
import org.json.JSONObject

class PluginStartRecordAudio : XMPPlugin() {
    override fun exec(f: XMPFragment?, w: XMPWebView?, data: JSONObject?, cbk: Callback?) {
        Util.log("js call $name")

        try {
            data class Out(val success: Boolean)

            val param = Util.toObj(data!!, JsUploadAudioParam::class.java)

            f?.let {
                AudioRecordUtil.stopRecordService(f.requireContext())
                AudioRecordUtil.startRecordService(f.requireContext(), param)
            }

            cbk?.complete(Util.toJSONObject(Out(success = true)))
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override fun getName(): String {
        return "start_record_audio"
    }

}