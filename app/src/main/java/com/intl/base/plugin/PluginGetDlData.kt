package com.intl.base.plugin

import com.intl.base.GlobalVar
import com.intl.base.Util
import com.xinye.xmp.bridge.XMPPlugin
import com.xinye.xmp.ui.XMPFragment
import com.xinye.xmp.ui.XMPWebView
import org.json.JSONObject

class PluginGetDlData : XMPPlugin() {
    override fun exec(f: XMPFragment?, w: XMPWebView?, data: JSONObject?, cbk: Callback?) {
        Util.log("js call $name")
        data class Out(val result: String)

        val url = GlobalVar.deepLink ?: ""

        cbk?.complete(Util.toJSONObject(Out(result = url)))
    }

    override fun getName(): String {
        return "get_dl_data"
    }

}