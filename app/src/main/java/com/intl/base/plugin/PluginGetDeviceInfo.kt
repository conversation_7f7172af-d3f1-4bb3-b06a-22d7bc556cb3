package com.intl.base.plugin

import com.intl.base.Util
import com.intl.lib_common.util.DeviceUtil
import com.xinye.xmp.bridge.XMPPlugin
import com.xinye.xmp.ui.XMPFragment
import com.xinye.xmp.ui.XMPWebView
import org.json.JSONObject

class PluginGetDeviceInfo : XMPPlugin() {
    override fun exec(f: XMPFragment?, w: XMPWebView?, data: JSONObject?, cbk: Callback?) {
        Util.log("js call $name")

        data class Out(val gaid: String, val aid: String)

        val gaid = DeviceUtil.getAdid(f!!.requireContext())
        val aid = DeviceUtil.getAndroidId(f!!.requireContext())

        cbk?.complete(Util.toJSONObject(Out(gaid = gaid, aid = aid)))
    }

    override fun getName(): String {
        return "get_device_info"
    }
}