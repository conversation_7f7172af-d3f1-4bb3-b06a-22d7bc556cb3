package com.intl.base.plugin

import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Bundle
import com.intl.base.GlobalVar
import com.intl.base.TempLog
import com.intl.base.Util
import com.xinye.xmp.bridge.XMPPlugin
import com.xinye.xmp.ui.XMPFragment
import com.xinye.xmp.ui.XMPWebView
import org.json.JSONObject

class PluginGetWaZeroTapOtp : XMPPlugin() {
    override fun exec(f: XMPFragment?, w: XMPWebView?, data: JSONObject?, cbk: Callback?) {
        Util.log("js call $name")
        TempLog.add("js调原生，开始监听wa otp")
        val context = f?.requireContext() ?: return
        // 1. 先和 WhatsApp 做握手
        handShake(context)
        // 2. 保存 callback 到全局变量
        GlobalVar.zeroTapOtpCallback = { otp ->
            TempLog.add("将otp回调给js, otp=$otp")
            data class Out(val otp: String)
            cbk?.complete(Util.toJSONObject(Out(otp)))
            GlobalVar.zeroTapOtpCallback = null
        }
    }

    private fun handShake(context: Context) {
        try {
            sendOtpIntentToWhatsApp(context, "com.whatsapp")
            sendOtpIntentToWhatsApp(context, "com.whatsapp.w4b")
        } catch (e: Exception) {
            TempLog.add("和wa握手失败，e=$e")
        }
    }

    private fun sendOtpIntentToWhatsApp(context: Context, packageName: String) {
        val flags = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) PendingIntent.FLAG_IMMUTABLE else 0
        val pi = PendingIntent.getActivity(
            context.applicationContext,
            0,
            Intent(),
            flags
        )
        val intentToWhatsApp = Intent().apply {
            setPackage(packageName)
            action = "com.whatsapp.otp.OTP_REQUESTED"
            val extras: Bundle = extras ?: Bundle()
            extras.putParcelable("_ci_", pi)
            putExtras(extras)
        }
        context.applicationContext.sendBroadcast(intentToWhatsApp)
    }

    override fun getName(): String {
        return "get_wa_zero_tap_otp"
    }
} 