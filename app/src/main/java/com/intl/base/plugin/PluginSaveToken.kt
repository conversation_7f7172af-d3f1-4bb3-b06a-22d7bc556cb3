package com.intl.base.plugin

import com.intl.base.SensorsDataUtil
import com.intl.base.UniqueKeys
import com.intl.base.Util
import com.intl.lib_common.util.PrefUtil
import com.xinye.xmp.bridge.XMPPlugin
import com.xinye.xmp.ui.XMPFragment
import com.xinye.xmp.ui.XMPWebView
import org.json.JSONObject

class PluginSaveToken : XMPPlugin() {
    override fun exec(f: XMPFragment?, w: XMPWebView?, data: JSONObject?, cbk: Callback?) {
        Util.log("js call $name")
        data class In(val token: String, val openId: String)
        data class Out(val success: Boolean)

        val p = Util.toObj(data!!, In::class.java)
        PrefUtil.putString(f!!.requireContext(), UniqueKeys.USER_TOKEN, p.token)
        PrefUtil.putString(f!!.requireContext(), UniqueKeys.OPEN_ID, p.openId)
        SensorsDataUtil.login(p.openId)

        cbk?.complete(Util.toJSONObject(Out(success = true)))
    }

    override fun getName(): String {
        return "save_token"
    }

}