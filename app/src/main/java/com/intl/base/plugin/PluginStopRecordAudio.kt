package com.intl.base.plugin

import com.intl.base.AudioRecordUtil
import com.intl.base.Util
import com.xinye.xmp.bridge.XMPPlugin
import com.xinye.xmp.ui.XMPFragment
import com.xinye.xmp.ui.XMPWebView
import org.json.JSONObject

class PluginStopRecordAudio : XMPPlugin() {
    override fun exec(f: XMPFragment?, w: XMPWebView?, data: JSONObject?, cbk: Callback?) {
        Util.log("js call $name")

        data class Out(val success: Boolean)

        AudioRecordUtil.stopRecordService(f!!.requireContext())
        cbk?.complete(Util.toJSONObject(Out(success = true)))
    }

    override fun getName(): String {
        return "stop_record_audio"
    }

}