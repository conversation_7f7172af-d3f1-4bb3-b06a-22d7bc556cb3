package com.intl.base.plugin

import com.intl.base.Util
import com.intl.lib_common.Zoloz
import com.xinye.xmp.bridge.XMPPlugin
import com.xinye.xmp.ui.XMPFragment
import com.xinye.xmp.ui.XMPWebView
import org.json.JSONObject

class PluginZolozGetMeta : XMPPlugin() {
    override fun exec(f: XMPFragment?, w: XMPWebView?, data: JSONObject?, cbk: Callback?) {
        Util.log("js call $name")

        data class Out(val meta: String)

        val meta = Zoloz.getMeta(f!!.requireContext())
        cbk?.complete(Util.toJSONObject(Out(meta)))
    }

    override fun getName(): String {
        return "zoloz_get_meta"
    }
}