package com.intl.base.plugin

import android.annotation.SuppressLint
import android.app.Application
import android.content.Context
import android.location.Location
import android.location.LocationListener
import android.location.LocationManager
import android.os.Handler
import android.os.HandlerThread
import android.os.Looper
import android.util.Log
import com.intl.base.Util
import com.xinye.xmp.bridge.XMPPlugin
import com.xinye.xmp.ui.XMPFragment
import com.xinye.xmp.ui.XMPWebView
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.json.JSONObject
import java.text.DateFormat
import java.util.Date

class PluginGetGps : XMPPlugin() {
    @SuppressLint("MissingPermission")
    override fun exec(f: XMPFragment?, w: XMPWebView?, data: JSONObject?, cbk: Callback?) {
        Util.log("js调安卓, $name")

        try {
            CoroutineScope(Dispatchers.IO).launch {
                Log.d("mayqx", "开始定位")
                var bestLocation: Location? = null
                try {
                    val locationManager = f!!.requireContext().getSystemService(Context.LOCATION_SERVICE) as? LocationManager
                    if (locationManager != null) {
                        val locationListener = HackedLocationListener()

                        val handlerThread = HandlerThread("MyHandlerThread")
                        handlerThread.start()
                        val looper = handlerThread.looper

                        val providers = locationManager.getProviders(true)
                        for (provider in providers) {
                            locationManager.requestLocationUpdates(provider, 1000, 0f, locationListener, looper)
                        }

                        // 等定位完成，等太短来不及定位，等太久导致采集慢
                        Log.d("mayqx", "睡一会儿")
                        delay(3 * 1000)
                        Log.d("mayqx", "睡醒了")

                        for (provider in providers) {
                            val location = locationManager.getLastKnownLocation(provider)
                            if (location != null && Math.abs(location.time - System.currentTimeMillis()) < 30 * 1000 //30s内的视为新鲜定位
                                && (bestLocation == null || location.accuracy < bestLocation.accuracy)
                            ) {
                                Log.d(
                                    "mayqx",
                                    """
                                    找到了更优值: $provider, ${
                                        DateFormat.getDateTimeInstance().format(Date(location.time))
                                    }, ${location.accuracy}, ${location.getLatitude()},  ${location.getLongitude()}
                                    """.trimIndent()
                                )
                                bestLocation = location
                            }
                        }

                        locationManager.removeUpdates(locationListener)
                    }

                    if (bestLocation != null) {
                        val decode = Util.decode(f.requireContext(), bestLocation)
                        Util.log("读取位置结束，location=$bestLocation, decode=$decode")
                        withContext(Dispatchers.Main) { //要回到主线程调js，否则无效
                            cbk?.complete(Util.toJSONObject(decode))
                        }
                    } else {
                        Util.log("读取位置失败")
                        withContext(Dispatchers.Main) {
                            cbk?.complete(null)
                        }
                    }
                } catch (exception: java.lang.Exception) {
                    withContext(Dispatchers.Main) {
                        cbk?.complete(null)
                    }
                }
            }
        } catch (e: Exception) {
            cbk?.complete(null)
        }
    }

    override fun getName(): String {
        return "get_gps"
    }
}