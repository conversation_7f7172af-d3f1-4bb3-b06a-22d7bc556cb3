package com.intl.base.plugin

import com.intl.base.GlobalVar
import com.intl.base.Util
import com.xinye.xmp.bridge.XMPPlugin
import com.xinye.xmp.ui.XMPFragment
import com.xinye.xmp.ui.XMPWebView
import org.json.JSONObject

class PluginClearDlData : XMPPlugin() {
    override fun exec(f: XMPFragment?, w: XMPWebView?, data: JSONObject?, cbk: Callback?) {
        Util.log("js call $name")
        data class Out(val success: Boolean)

        GlobalVar.deepLink = null

        cbk?.complete(Util.toJSONObject(Out(success = true)))
    }

    override fun getName(): String {
        return "clear_dl_data"
    }

}