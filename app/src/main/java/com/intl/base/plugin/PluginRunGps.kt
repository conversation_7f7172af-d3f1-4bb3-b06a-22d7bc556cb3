package com.intl.base.plugin

import android.os.CountDownTimer
import com.intl.base.GlobalVar
import com.intl.base.Util
import com.intl.bu.model.api.ApiUploadPrivacyData
import com.intl.lib_common.util.DataCollectionSdkUtil
import com.ppdai.module.datacollection.AcType
import com.xinye.xmp.bridge.XMPPlugin
import com.xinye.xmp.ui.XMPFragment
import com.xinye.xmp.ui.XMPWebView
import org.json.JSONObject
import java.io.IOException

class PluginRunGps : XMPPlugin() {

    override fun exec(f: XMPFragment?, w: XMPWebView?, data: JSONObject?, cbk: Callback?) {
        Util.log("js call $name")
        data class In(val interval: Long?, val url: String, val headers: Map<String, String>)
        data class Out(val success: Boolean)

        val p = Util.toObj(data!!, In::class.java)
        val ms = (p.interval ?: 6 * 60) * 1000 //默认6分钟

        GlobalVar.timer?.cancel() //防止js重复调用，注册多个timer
        GlobalVar.timer = object : CountDownTimer(Long.MAX_VALUE, ms) {
            override fun onTick(millisUntilFinished: Long) {
                DataCollectionSdkUtil.collect(AcType.locationInfo) { map, s, resultCallback ->
                    Util.post(
                        ApiUploadPrivacyData(p.url),
                        p.headers,
                        ApiUploadPrivacyData.decodeSdkData(map),
                        object : Util.TypeCallback<ApiUploadPrivacyData.Out> {
                            override fun onResponse(httpSuccess: Boolean, responseBody: ApiUploadPrivacyData.Out?) {
                                resultCallback?.uploadSuccess("success")
                            }

                            override fun onFailure(e: IOException) {
                                resultCallback?.uploadFail(e)
                            }

                        }
                    )
                }
            }

            override fun onFinish() {
                //
            }
        }
        GlobalVar.timer?.start()

        cbk?.complete(Util.toJSONObject(Out(true)))
    }

    override fun getName(): String {
        return "run_gps"
    }
}