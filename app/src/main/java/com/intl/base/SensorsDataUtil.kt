package com.intl.base

import android.app.Activity
import android.content.Context
import android.text.TextUtils
import android.util.Log
import android.webkit.WebView
import androidx.fragment.app.Fragment
import com.google.android.gms.ads.identifier.AdvertisingIdClient
import com.intl.BuildConfig
import com.intl.lib_common.util.DeviceUtil
import com.sensorsdata.analytics.android.sdk.SAConfigOptions
import com.sensorsdata.analytics.android.sdk.SensorsAnalyticsAutoTrackEventType
import com.sensorsdata.analytics.android.sdk.SensorsDataAPI
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.json.JSONException
import org.json.JSONObject

/**
 * Created by taiyating on 2017/8/1.
 * 神策工具类
 *
 *
 * https://manual.sensorsdata.cn/sa/3.0/zh_cn/android-7541696.html
 */
object SensorsDataUtil {
    // 数据接收的 URL
    private val SA_SERVER_URL = "https://sensors.yessscredit.id/sa?project=cashcerdas"

    /**
     * 初始化 SDK
     */
    fun init(context: Context) {
        //设置 SAConfigOptions，传入数据接收地址 SA_SERVER_URL
        val saConfigOptions = SAConfigOptions(SA_SERVER_URL)
        //通过 SAConfigOptions 设置神策 SDK，每个条件都非必须，开发者可根据自己实际情况设置，更多设置可参考 SAConfigOptions 类中方法注释
        saConfigOptions.setAutoTrackEventType(
            SensorsAnalyticsAutoTrackEventType.APP_CLICK or  // 开启全埋点点击事件
                SensorsAnalyticsAutoTrackEventType.APP_START or  //开启全埋点启动事件
                SensorsAnalyticsAutoTrackEventType.APP_END or  //开启全埋点退出事件
                SensorsAnalyticsAutoTrackEventType.APP_VIEW_SCREEN
        ) //开启全埋点浏览事件
            .enableJavaScriptBridge(true) //App打通H5
            .enableLog(BuildConfig.DEBUG) //开启神策调试日志，默认关闭(调试时，可开启日志)。
            .enableTrackPageLeave(true, true) //采集页面浏览时长
            .enableTrackAppCrash() //开启 crash 采集
        if (BuildConfig.DEBUG) setDebugFlush(saConfigOptions)
        //需要在主线程初始化神策 SDK
        SensorsDataAPI.startWithConfigOptions(context, saConfigOptions)
        setSuperProperties(context)
        //初始化 SDK 之后，开启自动采集 Fragment 页面浏览事件
        SensorsDataAPI.sharedInstance().trackFragmentAppViewScreen()
    }

    /**
     * 神策默认值：flushInterval 15s;
     * 这里为了测试方便，我们分别设置为3s发送一次;
     */
    private fun setDebugFlush(saConfigOptions: SAConfigOptions) {
        saConfigOptions.setFlushInterval(3000)
    }

    /**
     * 注册事件的公共属性。后面所有track的事件都将添加这里注册的属性。
     */
    private fun setSuperProperties(context: Context) {
        try {
            val prop1 = JSONObject()
            prop1.put("android_id", DeviceUtil.getAndroidId(context))
            SensorsDataAPI.sharedInstance().registerSuperProperties(prop1)

            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val prop2 = JSONObject()
                    val adInfo = AdvertisingIdClient.getAdvertisingIdInfo(context)
                    prop2.put("ad_id", adInfo.id)
                    SensorsDataAPI.sharedInstance().registerSuperProperties(prop2)
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        } catch (e: JSONException) {
            e.printStackTrace()
        }
    }

    /**
     * 用户登录
     *
     * @param openId 用户ID
     */
    fun login(openId: String?) {
        if (!TextUtils.isEmpty(openId)) {
            SensorsDataAPI.sharedInstance().login(openId)
        }
    }

    /**
     * 用户登出
     */
    fun logout() {
        SensorsDataAPI.sharedInstance().logout()
    }

    /**
     * 强制上传数据
     */
    fun flush() {
        SensorsDataAPI.sharedInstance().flush()
    }

    /**
     * 调用track接口，追踪一个带有属性的事件
     *
     * @param eventName  事件的名称
     * @param properties 事件的属性
     */
    private fun trackEvent(eventName: String, properties: JSONObject) {
        SensorsDataAPI.sharedInstance().track(eventName, properties)
    }

    fun trackViewScreen(activity: Activity?) {
        SensorsDataAPI.sharedInstance().trackViewScreen(activity)
    }

    fun trackViewScreen(url: String?, properties: JSONObject?) {
        SensorsDataAPI.sharedInstance().trackViewScreen(url, properties)
    }

    fun trackViewScreen(fragment: Fragment?) {
        SensorsDataAPI.sharedInstance().trackViewScreen(fragment)
    }

    /**
     * 打通APP与H5
     * https://manual.sensorsdata.cn/sa/latest/tech_sdk_client_link-7551956.html
     * 按照文档的做法其实已足够，但目前编译发现：
     * Plugin 尝试扫描 WebView 的实例对象，用于自动插入打通 H5 的代码，但是可能会失败，没能自动插入 showUpWebView 方法
     * 所以最好在AWeb中手动调用这个方法
     * 实测发现无需手动调用，也能正常打通
     */
    fun showUpWebView(webView: WebView?) {
        SensorsDataAPI.sharedInstance().showUpWebView(webView, true, true)
    }

    /**
     * 更新 GPS 位置信息
     *
     * @param latitude  纬度
     * @param longitude 经度
     */
    fun setGPSLocation(latitude: Double, longitude: Double) {
        SensorsDataAPI.sharedInstance().setGPSLocation(latitude, longitude)
    }

    /**
     * 记录激活事件
     *
     * @param context  context
     * @param source   AppsFlyer 记录的 media_source
     * @param client   AppsFlyer 记录的 agency
     * @param campaign AppsFlyer 记录的 campaign
     */
    fun trackInstallation(context: Context?, source: String?, client: String?, campaign: String?, adsetId: String?) {
        try {
            val properties = JSONObject()
            properties.put("source", source)
            properties.put("client", client)
            properties.put("campaign", campaign)
            properties.put("adsetId", adsetId)
            //记录 AppInstall 激活事件
            SensorsDataAPI.sharedInstance().trackInstallation("AppInstall", properties)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 点击事件
     *
     * @param page       所在页面
     * @param tgtEventId 元素id
     * @param tgtName    元素名称
     * @param paramMap   除了三个固定参数外，后面可能有多个param，param1... 所以用map替代
     */
    fun appClick(page: String?, tgtEventId: String?, tgtName: String?, paramMap: HashMap<String, Any>?) {
        try {
            val properties = JSONObject()
            properties.put("page", page)
            properties.put("tgt_event_id", tgtEventId)
            properties.put("tgt_name", tgtName)
            param2Json(properties, paramMap)
            trackEvent(EventName.APP_CLK, properties)
        } catch (e: JSONException) {
            e.printStackTrace()
        }
    }

    /**
     * 元素曝光
     */
    fun appElementImp(page: String?, tgtEventId: String?, tgtName: String?, paramMap: HashMap<String, Any>?) {
        try {
            val properties = JSONObject()
            properties.put("page", page)
            properties.put("tgt_event_id", tgtEventId)
            properties.put("tgt_name", tgtName)
            param2Json(properties, paramMap)
            trackEvent(EventName.APP_ELEMENT_IMP, properties)
        } catch (e: JSONException) {
            e.printStackTrace()
        }
    }

    /**
     * 输入事件
     * 失焦时触发
     *
     * @param costTimeMs 为0时，不传这个字段
     */
    fun appInput(page: String?, tgtEventId: String?, tgtName: String?, param: String, costTimeMs: Long) {
        val paramMap = HashMap<String, Any>()
        paramMap["param"] = param
        if (costTimeMs != 0L) {
            paramMap["cost_time_ms"] = costTimeMs
        }
        appInput(page, tgtEventId, tgtName, paramMap)
    }

    /**
     * 输入事件
     */
    fun appInput(page: String?, tgtEventId: String?, tgtName: String?, paramMap: HashMap<String, Any>?) {
        try {
            val properties = JSONObject()
            properties.put("page", page)
            properties.put("tgt_event_id", tgtEventId)
            properties.put("tgt_name", tgtName)
            param2Json(properties, paramMap)
            trackEvent(EventName.APP_INPUT, properties)
        } catch (e: JSONException) {
            e.printStackTrace()
        }
    }

    /**
     * 认证事件
     */
    fun appAuth(page: String?, authTgt: String?, authTgtName: String?, paramMap: HashMap<String, Any>?) {
        try {
            val properties = JSONObject()
            properties.put("page", page)
            properties.put("auth_tgt", authTgt)
            properties.put("auth_tgt_name", authTgtName)
            param2Json(properties, paramMap)
            trackEvent(EventName.APP_AUTH, properties)
        } catch (e: JSONException) {
            e.printStackTrace()
        }
    }

    /**
     * 认证事件
     *
     * @param page        页面名称
     * @param authTgt     id
     * @param authTgtName name
     * @param retCode     1代表成功，0代表失败，-99代表未知
     * @param errMsg      接口返回的认证失败原因
     */
    fun appAuth(page: String?, authTgt: String?, authTgtName: String?, retCode: Int, errMsg: String) {
        val paramMap = HashMap<String, Any>()
        paramMap["ret_code"] = retCode
        paramMap["err_msg"] = errMsg
        appAuth(page, authTgt, authTgtName, paramMap)
    }

    /**
     * 截屏事件
     */
    fun appScreenshot(page: String?, url: String?) {
        try {
            val properties = JSONObject()
            properties.put("page", page)
            properties.put("url", url)
            trackEvent(EventName.APP_SCREENSHOT, properties)
        } catch (e: JSONException) {
            e.printStackTrace()
        }
    }

    fun webViewEvent(paramMap: HashMap<String, Any>?) {
        try {
            val properties = JSONObject()
            param2Json(properties, paramMap)
            trackEvent(EventName.WEBVIEW, properties)
        } catch (e: JSONException) {
            e.printStackTrace()
        }
    }

    fun appEnterForeground() {
        try {
            val properties = JSONObject()
            trackEvent(EventName.APP_ENTER_FGD, properties)
        } catch (e: JSONException) {
            e.printStackTrace()
        }
    }

    fun appEnterBackground() {
        try {
            val properties = JSONObject()
            trackEvent(EventName.APP_ENTER_BKG, properties)
        } catch (e: JSONException) {
            e.printStackTrace()
        }
    }

    /**
     * 把map对象都数据放到json对象里，且value类型必须都是String类型，避免后面入库出现的数据类型不一致问题。
     */
    @Throws(JSONException::class)
    private fun param2Json(properties: JSONObject, paramMap: HashMap<String, Any>?) {
        if (paramMap != null && !paramMap.isEmpty()) {
            for (key in paramMap.keys) {
                val value = paramMap[key]
                properties.putOpt(key, value?.toString())
            }
        }
    }

    fun getParamMap(vararg params: Any): HashMap<String, Any>? {
        if (params == null || params.size == 0) {
            return null
        }
        val paramMap = HashMap<String, Any>()
        for (i in params.indices) {
            if (i == 0) {
                paramMap["param"] = params[i]
            } else {
                paramMap["param$i"] = params[i]
            }
        }
        return paramMap
    }

    private object EventName {
        const val APP_ENTER_FGD = "app_enter_foreground" // app进入前台
        const val APP_ENTER_BKG = "app_enter_background" // app进入后台
        const val APP_CLK = "app_clk" // 点击元素
        const val APP_ELEMENT_IMP = "app_element_imp" // app元素曝光
        const val APP_INPUT = "app_input" // app 输入事件
        const val APP_AUTH = "app_auth" // app auth
        const val APP_SCREENSHOT = "app_screenshot" // 截屏
        const val WEBVIEW = "webview" // webview事件
    }
}
