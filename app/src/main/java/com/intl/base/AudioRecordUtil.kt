package com.intl.base

import android.content.Context
import android.content.Intent
import android.util.Log
import androidx.core.content.ContextCompat
import androidx.work.Data
import androidx.work.ExistingWorkPolicy
import androidx.work.OneTimeWorkRequest
import androidx.work.WorkManager
import com.intl.bu.model.api.JsUploadAudioParam
import com.intl.bu.service.AudioRecorderService
import kotlin.random.Random

/**
 * Utility class for managing the RecorderService.
 */
object AudioRecordUtil {
    fun startRecordService(context: Context, param: JsUploadAudioParam) {
        try {
            val intent = Intent(context, AudioRecorderService::class.java).apply {
                putExtra(AudioRecorderService.KEY_PARAM, param)
            }
            ContextCompat.startForegroundService(context, intent)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }


    fun stopRecordService(context: Context) {
        try {
            val intent = Intent(context, AudioRecorderService::class.java)
            context.stopService(intent)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun startUploadTask(context: Context, param: JsUploadAudioParam, filePath: String) {
        try {
            Log.d("mayqx", "startUploadTask")
            // 构建输入数据
            val inputData = Data.Builder()
                .putString(AudioUploadWorker.KEY_HEADERS, Util.toJSONString(param.headers))
                .putString(AudioUploadWorker.KEY_BODY, Util.toJSONString(param.body))
                .putString(AudioUploadWorker.KEY_URL, param.url)
                .putString(AudioUploadWorker.KEY_FILE_PATH, filePath)
                .build()

            // 创建 OneTimeWorkRequest，只执行一次
            val uploadWorkRequest = OneTimeWorkRequest.Builder(AudioUploadWorker::class.java)
                .setInputData(inputData) // 传递参数
                .build()

            // 将任务提交给 WorkManager
            WorkManager.getInstance(context).enqueueUniqueWork(
                Random.nextLong().toString(), // 指定唯一任务名称，每个任务负责上传一个文件
                ExistingWorkPolicy.KEEP, // 如果已经存在，保持
                uploadWorkRequest
            )
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun stopUploadTask(context: Context) {
        try {
            WorkManager.getInstance(context).cancelAllWork()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}
