package com.intl.base

import android.app.AlertDialog
import android.content.Context
import com.intl.R
import com.intl.bu.activity.PermissionShimActivity

object SpecialPermissionUtil {

    fun requestPermission(context: Context, permission: String, callback: (Boolean) -> Unit) {
        when (permission) {
            android.Manifest.permission.PACKAGE_USAGE_STATS -> {
                if (AppUsageUtil.isAppUsageEnabled(context)) {
                    callback(true)
                } else {
                    AlertDialog.Builder(context).setMessage(context.getString(R.string.enable_app_access))
                        .setPositiveButton("OK") { dlg, _ ->
                            run {
                                PermissionShimActivity.start(context, permission)
                                PermissionShimActivity.callback = callback
                                dlg.dismiss()
                            }
                        }.setCancelable(false).show()
                }
            }

            else -> {
                throw Exception("Not Supported Permission: $permission")
            }
        }
    }
}