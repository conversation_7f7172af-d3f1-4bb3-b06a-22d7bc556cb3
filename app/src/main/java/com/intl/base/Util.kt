package com.intl.base

import android.app.Application
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.location.Geocoder
import android.location.Location
import android.location.LocationManager
import android.net.Uri
import android.os.Build
import android.text.TextUtils
import android.util.Log
import android.widget.Toast
import androidx.annotation.WorkerThread
import androidx.core.location.LocationManagerCompat
import com.google.gson.Gson
import com.intl.BuildConfig
import com.intl.R
import com.intl.bu.model.Headers
import com.intl.bu.model.api.BaseApi
import com.intl.lib_common.util.NetworkUtil
import com.intl.lib_common.util.PrefUtil
import org.json.JSONObject
import java.io.File
import java.io.IOException
import java.lang.reflect.Type
import java.util.Locale

object Util {
    @WorkerThread
    fun decode(ctx: Context, loc: Location): LocationData {
        try {
            val geocoder = Geocoder(ctx, Locale.getDefault());

            if (!Geocoder.isPresent()) {
                Log.d("mayqx", "编码服务不存在")
            }

            val addresses = try {
                // pixel5测试机上，此方法抛异常
                geocoder.getFromLocation(
                    loc.latitude,
                    loc.longitude,
                    1,
                )
            } catch (e: Exception) {
                Log.d("mayqx", "编码服务异常")
                null
            }

            // 有时候反编译失败，但是基本的位置还是可以拿到的
            val first = if (addresses?.isNotEmpty() == true) addresses.get(0) else null

            val province = first?.adminArea ?: ""
            val city = first?.locality ?: ""
            val area = first?.subLocality ?: ""
            val address = (first?.thoroughfare ?: "") + (first?.subThoroughfare ?: "")

            val mock = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S /*Android12*/) {
                loc.isMock
            } else {
                loc.isFromMockProvider
            }

            if (mock) {
                Log.d("mayqx", "mock location!")
            } else {
                Log.d("mayqx", "real location")
            }

            //Toast.makeText(ctx, "反编码成功：$province, $city, $area, $address", Toast.LENGTH_SHORT).show()
            return LocationData(
                latitude = loc.latitude,
                longitude = loc.longitude,
                accuracy = loc.accuracy,
                province = province,
                city = city,
                area = area,
                address = address,
                mock = mock,
                provider = loc.provider,
            )
        } catch (e: Exception) {
            Log.d("mayqx", "反编译异常,e=$e")
            //Toast.makeText(ctx, "反编码异常,e=$e", Toast.LENGTH_SHORT).show()
            return LocationData(
                latitude = loc.latitude,
                longitude = loc.longitude,
                accuracy = loc.accuracy,
                province = "",
                city = "",
                area = "",
                address = "",
                mock = false,
                provider = "",
            )
        }
    }

    fun toJSONObject(obj: Any): JSONObject {
        return JSONObject(Gson().toJson(obj))
    }

    fun toJSONString(obj: Any): String {
        return toJSONObject(obj).toString()
    }

    fun <T> toObj(json: String, clz: Class<T>): T {
        return Gson().fromJson(json, clz)
    }

    fun <T> toObj(json: JSONObject, clz: Class<T>): T {
        return toObj(json.toString(), clz)
    }

    fun <T> toObj(json: String, type: Type): T {
        return Gson().fromJson(json, type)
    }

    fun log(msg: String) {
        Log.d("mayqx", msg)
    }

    fun toast(ctx: Context, msg: String) {
        Toast.makeText(ctx, msg, Toast.LENGTH_SHORT).show()
    }

    /**
     * 这一层封装主要是解决序列化和反序列化问题
     * @param T
     * @param token
     * @param url
     * @param inParam
     * @param clz
     * @param callback
     */
    fun <T> post(token: String?, url: String, inParam: Any, clz: Class<T>, callback: TypeCallback<T>) {
        NetworkUtil.post(
            url,
            Headers.build(token),
            toJSONString(inParam),
            object : NetworkUtil.MyCallback {
                override fun onResponse(httpSuccess: Boolean, responseBody: String) {
                    val o = toObj(responseBody, clz)
                    callback.onResponse(httpSuccess, o)
                }

                override fun onFailure(e: IOException) {
                    callback.onFailure(e)
                }
            })
    }

    /**
     * 这一层封装主要是解决序列化和反序列化问题
     * @param T
     * @param token
     * @param api
     * @param requestBody
     * @param callback
     */
    fun <T> post(token: String?, api: BaseApi<T>, requestBody: Any, callback: TypeCallback<T>) {
        NetworkUtil.post(
            api.url(),
            Headers.build(token),
            toJSONString(requestBody),
            object : NetworkUtil.MyCallback {
                override fun onResponse(httpSuccess: Boolean, responseBody: String) {
                    val o = toObj(responseBody, api.outClass())
                    callback.onResponse(httpSuccess, o)
                }

                override fun onFailure(e: IOException) {
                    callback.onFailure(e)
                }
            })
    }

    /**
     * 由js提供url和header
     * 支持反序列化
     * @param T
     * @param token
     * @param api
     * @param requestBody
     * @param callback
     */
    fun <T> post(api: BaseApi<T>, headers: Map<String, String>, requestBody: Any, callback: TypeCallback<T>) {
        NetworkUtil.post(api.url(), headers, toJSONString(requestBody), object : NetworkUtil.MyCallback {
            override fun onResponse(httpSuccess: Boolean, responseBody: String) {
                val o = try {
                    toObj(responseBody, api.outClass())
                } catch (e: Exception) {
                    null
                }
                callback.onResponse(httpSuccess, o)
            }

            override fun onFailure(e: IOException) {
                callback.onFailure(e)
            }
        })
    }

    /**
     * 故意不捕捉异常，往上抛
     */
    @Throws(IOException::class)
    fun <T> postFileSync(
        api: BaseApi<T>,
        headers: Map<String, String>,
        body: Map<String, String>,
        formFieldName: String,
        file: File,
    ): T {
        // 执行同步请求
        val response = NetworkUtil.postFile(api.url(), headers, body, formFieldName, file)

        // 判断响应是否成功
        if (response.isSuccessful) {
            // 反序列化响应体
            val responseBody = response.body()?.string()
            // 使用 Gson 进行反序列化
            responseBody?.let {
                return toObj(it, api.outClass())
            }
        }

        throw Exception(response.toString())
    }


    interface TypeCallback<T> {
        /**
         * @param httpSuccess
         * @param responseBody 反序列化失败场景下为空
         */
        fun onResponse(httpSuccess: Boolean, responseBody: T?)

        fun onFailure(e: IOException)
    }

    open class MyTypeCallback<T> : TypeCallback<T> {
        override fun onResponse(httpSuccess: Boolean, responseBody: T?) {
            //
        }

        override fun onFailure(e: IOException) {
            //
        }
    }

    fun getBaseUrl(context: Context): String {
        if (BuildConfig.DEBUG) {
            return PrefUtil.getString(context, UniqueKeys.BASE_URL) ?: Constant.fat
        } else {
            return Constant.rls
        }
    }

    fun copyToClip(context: Context, content: String?, vararg customToastOnSuccess: String?) {
        try {
            if (!TextUtils.isEmpty(content)) {
                val cm = context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
                val data = ClipData.newPlainText(null, content)
                cm.setPrimaryClip(data)
                val text = if (customToastOnSuccess.isNotEmpty() && customToastOnSuccess[0]?.isNotEmpty() == true) {
                    customToastOnSuccess[0]
                } else {
                    ""
                }
                text?.let { toast(context, it) }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    // 2个常用的启动Activity的模式
    const val FLAG_CLEAR_STACK = Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK
    const val FLAG_CLEAR_TOP = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP

    fun getDeepLink(context: Context, intent: Intent): String? {
        return try {
            val uri = Uri.parse(intent.dataString)
            if (isAppLink(context, uri) || isURLScheme(context, uri)) {
                intent.dataString
            } else {
                null
            }
        } catch (e: Exception) {
            null
        }
    }

    private fun isAppLink(context: Context, uri: Uri): Boolean {
        return uri.scheme.equals("https")
            && uri.host.equals(context.getString(R.string.dl_host))
            && uri.path.equals(context.getString(R.string.dl_path))
    }

    private fun isURLScheme(context: Context, uri: Uri): Boolean {
        return uri.scheme.equals(context.getString(R.string.dl_custom_scheme))
    }

    fun isLocationEnabled(context: Context): Boolean {
        val mgr = context.getSystemService(Application.LOCATION_SERVICE) as LocationManager
        return LocationManagerCompat.isLocationEnabled(mgr)
    }

    fun goLocationSetting(context: Context) {
        context.startActivity(Intent(android.provider.Settings.ACTION_LOCATION_SOURCE_SETTINGS))
    }

    /**
     * 获取安装器包名
     *
     * @param context
     * @return
     */
    fun getInstaller(context: Context): String? {
        return try {
            var debugInfo = ""
            val myPkgName = context.packageName
            var installer: String? = null
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                debugInfo += "安卓>=11"
                try {
                    installer = context.packageManager.getInstallSourceInfo(myPkgName).installingPackageName
                } catch (e: PackageManager.NameNotFoundException) {
                    e.printStackTrace()
                }
            } else {
                debugInfo += "安卓<11"
                installer = context.packageManager.getInstallerPackageName(myPkgName)
            }
            debugInfo += "，安装器=$installer"
            Log.d("mayqx", debugInfo)
            installer
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
            ""
        }
    }

    fun isLogin(context: Context): Boolean {
        val token = PrefUtil.getString(context, UniqueKeys.USER_TOKEN)
        return token?.isNotEmpty() ?: false
    }

    fun getOpenId(context: Context): String {
        return PrefUtil.getString(context, UniqueKeys.OPEN_ID) ?: ""
    }
}
