package com.intl.base

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.app.PendingIntent

class OtpCodeReceiver : BroadcastReceiver() {
    override fun onReceive(context: Context, intent: Intent) {
        TempLog.add("收到wa发来的otp")
        // 检查 PendingIntent 来源
        val pendingIntent = intent.getParcelableExtra<PendingIntent>("_ci_")
        val creatorPackage = pendingIntent?.creatorPackage
        if (creatorPackage == "com.whatsapp" || creatorPackage == "com.whatsapp.w4b") {
            // 读取OTP
            val otpCode = intent.getStringExtra("code")

            TempLog.add("wa发来的otp=$otpCode")

            // 优先回调给 js
            if (otpCode != null) {
                GlobalVar.zeroTapOtpCallback?.invoke(otpCode)
            }

        } else {
            TempLog.add("wa发来的otp，但是来源不是wa，而是$creatorPackage")
        }
    }
} 