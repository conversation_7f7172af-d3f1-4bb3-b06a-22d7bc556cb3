package com.intl.base

import android.app.Application
import android.content.IntentFilter
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ProcessLifecycleOwner
import com.intl.base.plugin.PluginGetWaZeroTapOtp
import com.intl.base.plugin.PluginCheckPermission
import com.intl.base.plugin.PluginClearDlData
import com.intl.base.plugin.PluginCollectPrivacyData
import com.intl.base.plugin.PluginCollectTongdun
import com.intl.base.plugin.PluginTestDns
import com.intl.base.plugin.PluginGetAdaDeviceRisk
import com.intl.base.plugin.PluginGetAppInfo
import com.intl.base.plugin.PluginGetData
import com.intl.base.plugin.PluginGetDeviceInfo
import com.intl.base.plugin.PluginGetDlData
import com.intl.base.plugin.PluginGetGps
import com.intl.base.plugin.PluginGetPushToken
import com.intl.base.plugin.PluginOpenUrlInBrowser
import com.intl.base.plugin.PluginPickContact
import com.intl.base.plugin.PluginRemoveData
import com.intl.base.plugin.PluginReport
import com.intl.base.plugin.PluginRequestPermission
import com.intl.base.plugin.PluginRunGps
import com.intl.base.plugin.PluginSaveToken
import com.intl.base.plugin.PluginScan
import com.intl.base.plugin.PluginSetData
import com.intl.base.plugin.PluginSetScreenshot
import com.intl.base.plugin.PluginShareImage
import com.intl.base.plugin.PluginStartRecordAudio
import com.intl.base.plugin.PluginStopRecordAudio
import com.intl.base.plugin.PluginZolozGetMeta
import com.intl.base.plugin.PluginZolozStartDetect
import com.intl.bu.TongDunKey
import com.intl.bu.model.Headers
import com.intl.bu.service.VolumeChangeReceiver
import com.intl.lib_common.util.DataCollectionSdkUtil
import com.intl.lib_common.util.DeviceUtil
import com.intl.lib_common.util.TongDunUtil
import com.xinye.xmp.XMP
import com.xinye.xmp.permission.PermissionTerminator

/**
 *
 */
class BaseApplication : Application(), DefaultLifecycleObserver {
    private var mVolumeChangeReceiver: VolumeChangeReceiver? = null

    override fun onCreate() {
        super<Application>.onCreate()
        XMP.init(this)
        // XMP.addPlugin();
        addPlugins()

        PermissionTerminator.init(this)
        TongDunUtil.init(TongDunKey.partnerCode, TongDunKey.appName, TongDunKey.appKey)
        Headers.init(this)
        DataCollectionSdkUtil.init(this)
        DeviceUtil.init(this)
        ProcessLifecycleOwner.get().lifecycle.addObserver(this)
        SensorsDataUtil.init(this)
    }

    private fun addPlugins() {
        XMP.addPlugin(PluginCollectPrivacyData())
        XMP.addPlugin(PluginCollectTongdun())
        XMP.addPlugin(PluginGetAdaDeviceRisk())
        XMP.addPlugin(PluginGetAppInfo())
        XMP.addPlugin(PluginGetDeviceInfo())
        XMP.addPlugin(PluginGetGps())
        XMP.addPlugin(PluginGetPushToken())
        XMP.addPlugin(PluginRequestPermission())
        XMP.addPlugin(PluginRunGps())
        XMP.addPlugin(PluginSaveToken())
        XMP.addPlugin(PluginScan())
        XMP.addPlugin(PluginZolozGetMeta())
        XMP.addPlugin(PluginZolozStartDetect())
        XMP.addPlugin(PluginGetDlData())
        XMP.addPlugin(PluginClearDlData())
        XMP.addPlugin(PluginSetData())
        XMP.addPlugin(PluginGetData())
        XMP.addPlugin(PluginRemoveData())
        XMP.addPlugin(PluginOpenUrlInBrowser())
        XMP.addPlugin(PluginCheckPermission())
        XMP.addPlugin(PluginSetScreenshot())
        XMP.addPlugin(PluginStartRecordAudio())
        XMP.addPlugin(PluginStopRecordAudio())
        XMP.addPlugin(PluginPickContact())
        XMP.addPlugin(PluginReport())
        XMP.addPlugin(PluginTestDns())
        XMP.addPlugin(PluginShareImage())
        XMP.addPlugin(PluginGetWaZeroTapOtp())
    }

    override fun onStart(owner: LifecycleOwner) {
        super.onStart(owner)
        //Log.d("mayqx", "app enter foreground")
        monitorVolumeChange()
        SensorsDataUtil.appEnterForeground()
    }

    override fun onStop(owner: LifecycleOwner) {
        super.onStop(owner)
        //Log.d("mayqx", "app enter background")
        stopMonitorVolumeChange()
        SensorsDataUtil.appEnterBackground()
    }

    private fun monitorVolumeChange() {
        try {
            mVolumeChangeReceiver = VolumeChangeReceiver()
            val filter = IntentFilter("android.media.VOLUME_CHANGED_ACTION")
            registerReceiver(mVolumeChangeReceiver, filter)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun stopMonitorVolumeChange() {
        try {
            unregisterReceiver(mVolumeChangeReceiver)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }


}
