package com.intl.base

import android.content.Context
import android.util.AttributeSet
import com.flyco.tablayout.CommonTabLayout
import com.flyco.tablayout.listener.OnTabSelectListener

/**
 * 可设置tab是否可点击
 *
 * @constructor
 *
 * @param context
 * @param attrs
 */
class CustomCommonTabLayout(context: Context, attrs: AttributeSet) : CommonTabLayout(context, attrs) {
    private var canClick = true
    private var listenerCache: OnTabSelectListener? = null

    fun setCanClick(can: <PERSON><PERSON><PERSON>) {
        canClick = can

        if (can) {
            if (listenerCache != null) {
                super.setOnTabSelectListener(listenerCache)
            }
        } else {
            super.setOnTabSelectListener(null)
        }
    }

    override fun setOnTabSelectListener(listener: OnTabSelectListener?) {
        listenerCache = listener

        if (canClick) {
            super.setOnTabSelectListener(listener)
        } else {
            //
        }
    }

    override fun setCurrentTab(currentTab: Int) {
        if (canClick) {
            super.setCurrentTab(currentTab)
        } else {
            //
        }
    }
}
