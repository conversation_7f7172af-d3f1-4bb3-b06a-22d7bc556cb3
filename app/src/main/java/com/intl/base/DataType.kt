package com.intl.base

import android.Manifest
import android.os.Build
import com.ppdai.module.datacollection.AcType

/**
 * @param key: 和js约定的值，不要单方面改
 */
enum class DataType(val key: String, val permission: String?, val acType: AcType?) {
    DEVICE("device", null, AcType.mobileInfo),
    NETWORK("network", null, AcType.network),
    APP_LIST("appList", null, AcType.applicationInfo),
    NOTIFICATION("notification", if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) Manifest.permission.POST_NOTIFICATIONS else null, null),
    CAMERA("camera", Manifest.permission.CAMERA, null),
    CONTACT("contact", Manifest.permission.READ_CONTACTS, AcType.contacts),
    LOCATION("location", Manifest.permission.ACCESS_FINE_LOCATION, AcType.locationInfo),
    WIFI_LIST("wifiList", Manifest.permission.ACCESS_FINE_LOCATION, AcType.wifiList),
    SMS("sms", Manifest.permission.READ_SMS, AcType.messageRecord),
    CALL_LOG("callLog", Manifest.permission.READ_CALL_LOG, AcType.callRecord),
    DETECT_SCREENSHOT(
        "detectScreenshot",
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) Manifest.permission.READ_MEDIA_IMAGES else Manifest.permission.READ_EXTERNAL_STORAGE,
        null
    ),
    APP_USAGE(
        "appUsage",
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) Manifest.permission.PACKAGE_USAGE_STATS else null,
        AcType.appUsage
    ),
    AUDIO("audio", Manifest.permission.RECORD_AUDIO, null),
    ;


    companion object {
        fun get(key: String) = values().firstOrNull { it.key == key }
    }
}