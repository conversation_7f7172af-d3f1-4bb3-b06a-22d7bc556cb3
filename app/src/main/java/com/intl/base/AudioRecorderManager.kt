package com.intl.base

import android.content.Context
import android.media.MediaRecorder
import android.os.Environment
import android.os.Handler
import android.os.Looper
import android.util.Log
import com.intl.bu.model.api.JsUploadAudioParam
import java.io.File

class AudioRecorderManager(private val context: Context, private val param: JsUploadAudioParam) {
    val TAG = "AudioRecorderManager"

    private var mediaRecorder: MediaRecorder? = null
    private var startTime: Long = 0L // 开始录音的时间戳
    private var currentFile: File? = null
    private val handler = Handler(Looper.getMainLooper())

    init {
        deleteAllTempFiles()
    }

    private fun getTheDirectoryPath(): String {
        // 用外部私有存储，容量大，无需权限
        return File(context.getExternalFilesDir(Environment.DIRECTORY_MUSIC), "recording").absolutePath
    }

    // 定时保存任务
    private val saveTask: Runnable = object : Runnable {
        override fun run() {
            Log.d(TAG, "saveTask.run")
            stopRecording() // 停止当前录音
            startRecording() // 重新开始录音
        }
    }

    /**
     * 开始录音
     */
    fun startRecording() {
        Log.d(TAG, "startRecording")
        // 记录开始时间戳
        startTime = System.currentTimeMillis()

        // 创建录音文件（仅包含开始时间戳）
        currentFile = createTempFile(startTime)
        Log.d(TAG, "currentFile=${currentFile?.absolutePath}")

        // 初始化 MediaRecorder
        // 以下参数适合录人声，要求清晰，体积小
        mediaRecorder = MediaRecorder().apply {
            setAudioSource(MediaRecorder.AudioSource.MIC)
            setOutputFormat(MediaRecorder.OutputFormat.MPEG_4) // 输出格式
            setAudioEncoder(MediaRecorder.AudioEncoder.AAC)   // 编码格式
            setAudioSamplingRate(16000)                      // 采样率
            setAudioEncodingBitRate(64000)                   // 比特率
            setOutputFile(currentFile?.absolutePath)
            prepare()
            start()
        }

        // 每隔 saveInterval 执行保存任务
        stopSaveTask() // 确保不会有重复的任务
        handler.postDelayed(saveTask, param.secondsPerFile * 1000L)
    }

    /**
     * 停止录音
     */
    fun stopRecording() {
        try {
            Log.d(TAG, "stopRecording")
            mediaRecorder?.apply {
                stop()
                release()
            }
            mediaRecorder = null

            stopSaveTask() // 停止延迟任务

            // 更新文件名，添加结束时间戳
            updateFileNameWithEndTime()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 停止保存任务
     */
    private fun stopSaveTask() {
        handler.removeCallbacks(saveTask)
    }

    /**
     * 更新文件名，添加结束时间戳
     */
    private fun updateFileNameWithEndTime() {
        Log.d(TAG, "updateFileNameWithEndTime")
        currentFile?.let { file ->
            val endTime = System.currentTimeMillis()
            val newFileName = "${startTime}-${endTime}.m4a"
            val newFile = File(file.parent, newFileName)
            if (file.renameTo(newFile)) {
                currentFile = newFile
            }
        }

        // 改名成功后，立即触发上传
        currentFile?.absolutePath?.let {
            AudioRecordUtil.startUploadTask(context, param, it)
        }
    }

    /**
     * 创建临时录音文件
     */
    private fun createTempFile(startTime: Long): File {
        Log.d(TAG, "createTempFile")
        // 外部私有存储
        val dir = File(getTheDirectoryPath())
        if (!dir.exists()) dir.mkdirs()

        val fileName = "${startTime}.tmp"
        return File(dir, fileName)
    }

    /**
     * 万一录音没有正常结束，生成了不完整的临时文件，我们最好删除他们，防止慢慢的占满存储
     * 注意删除时机，不要把录音中的文件删了
     */
    private fun deleteAllTempFiles() {
        Log.d(TAG, "deleteAllTempFiles")
        val dir = File(getTheDirectoryPath())
        if (dir.exists() && dir.isDirectory) {
            // 遍历目录中的文件，筛选出 .tmp 文件并删除
            dir.listFiles { file -> file.extension == "tmp" }?.forEach { tempFile ->
                if (tempFile.delete()) {
                    Log.d(TAG, "删除临时文件: ${tempFile.name}")
                } else {
                    Log.e(TAG, "删除临时文件失败: ${tempFile.name}")
                }
            }
        } else {
            Log.w(TAG, "指定目录不存在或者不是目录")
        }
    }

}


