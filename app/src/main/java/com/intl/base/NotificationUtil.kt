package com.intl.base

import android.Manifest
import android.app.Activity
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import android.provider.Settings
import android.util.Log
import androidx.core.app.ActivityCompat
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import androidx.core.content.ContextCompat
import com.intl.R

/**
 * Created by <PERSON> on 2019-04-30.
 *
 *
 * 描述：通知公共方法
 */
object NotificationUtil {
    const val CHANNEL_ID = "channel_id"
    val CHANNEL_NAME: CharSequence = "default_channel"
    const val CHANNEL_DESC = "default_channel"

    /**
     * 判断通知是否打开
     *
     * @return
     */
    fun isNotificationEnabled(context: Context?): Boolean {
        val notificationManager = NotificationManagerCompat.from(context!!)
        // 内部已经对版本做了适配
        return notificationManager.areNotificationsEnabled()
    }

    /**
     * 跳通知设置页面
     *
     * @param context
     * @param requestCode
     */
    fun goNotificationSetting(context: Activity, requestCode: Int) {
        try {
            val intent = Intent()
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                intent.setAction(Settings.ACTION_APP_NOTIFICATION_SETTINGS)
                intent.putExtra(Settings.EXTRA_APP_PACKAGE, context.packageName)
            } else {
                intent.setAction("android.settings.APP_NOTIFICATION_SETTINGS")
                intent.putExtra("app_package", context.packageName)
                intent.putExtra("app_uid", context.applicationInfo.uid)
            }
            context.startActivityForResult(intent, requestCode)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 获取申请通知权限的intent
     */
    fun getRequestPermissionIntent(context: Context): Intent {
        val intent = Intent()
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            intent.setAction(Settings.ACTION_APP_NOTIFICATION_SETTINGS)
            intent.putExtra(Settings.EXTRA_APP_PACKAGE, context.packageName)
        } else {
            intent.setAction("android.settings.APP_NOTIFICATION_SETTINGS")
            intent.putExtra("app_package", context.packageName)
            intent.putExtra("app_uid", context.applicationInfo.uid)
        }
        return intent
    }

    fun showNotification(context: Context?, title: String?, content: String?, intent: PendingIntent?) {
        val mBuilder: NotificationCompat.Builder = NotificationCompat.Builder(context!!, CHANNEL_ID)
            .setSmallIcon(R.drawable.notification)
            .setColor(ContextCompat.getColor(context, R.color.primary))
            .setContentTitle(title)
            .setContentText(content) //默认显示这个
            .setStyle(NotificationCompat.BigTextStyle().bigText(content)) //下拉后显示这个
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setContentIntent(intent)
            .setAutoCancel(true)

        // show notification
        val notificationManager = NotificationManagerCompat.from(context)
        // notificationId is a unique int for each notification that you must define
        // 注意：如果2条通知的id相同，则会覆盖
        val uniqueInt = System.currentTimeMillis().toInt()

        // notify()方法要求做权限检查
        if (ActivityCompat.checkSelfPermission(context, Manifest.permission.POST_NOTIFICATIONS) != PackageManager.PERMISSION_GRANTED) {
            return
        }
        notificationManager.notify(uniqueInt, mBuilder.build())
    }

    /**
     * >=Android O，必须创建通知渠道，否则无法显示通知
     * 在红伍的8.1.0手机上实测发现，的确要建推送渠道，否则无法显示通知
     */
    fun createNotificationChannel(context: Context) {
        // Create the NotificationChannel, but only on API 26+ because
        // the NotificationChannel class is new and not in the support library
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            Log.d("lhw", "create channel for OS >= O")
            val importance = NotificationManager.IMPORTANCE_DEFAULT
            val channel = NotificationChannel(CHANNEL_ID, CHANNEL_NAME, importance)
            channel.description = CHANNEL_DESC
            // Register the channel with the system; you can't change the importance
            // or other notification behaviors after this
            val notificationManager = context.getSystemService(NotificationManager::class.java)
            notificationManager?.createNotificationChannel(channel) ?: Log.d("lhw", "NotificationManager not found")
        } else {
            Log.d("lhw", "skip channel creation for OS < O")
        }
    }

    fun showGoSettingDialog(context: Activity, requestCode: Int) {
//        val content = (context.getString(R.string.please_grant_permission_in_setting)
//            + "Notifications")
//        val dialog = SimpleDialog(context)
//        dialog.setTitle(context.getString(R.string.operation_notice))
//            .setContent(content)
//            .setBtnOk(context.getString(R.string.go_setting)) { v -> goNotificationSetting(context, requestCode) }
//            .setBtnCancel()
//            .show()
    }
}
