package com.intl.base

import android.content.Context
import android.content.Intent
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import com.intl.lib_common.activity.CustomCaptureActivity
import com.king.camera.scan.CameraScan

class ScanProxyFragment : Fragment() {
    private var callback: Callback? = null

    override fun onAttach(context: Context) {
        super.onAttach(context)
        try {
            val intent = Intent(context, CustomCaptureActivity::class.java)
            startActivityForResult(intent, Companion.REQUEST_CODE)
        } catch (e: Exception) {
            removeAndCallback(null)
        }
    }

    private fun removeAndCallback(content: String?) {
        try {
            callback!!.onResult(content)
            requireActivity().supportFragmentManager.beginTransaction().remove(this).commitAllowingStateLoss()
        } catch (e: Exception) {
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == Companion.REQUEST_CODE && data != null) {
            try {
                val url = data.getStringExtra(CameraScan.SCAN_RESULT) ?: ""
                removeAndCallback(url)
            } catch (e: Exception) {
                removeAndCallback(null)
            }
        } else {
            removeAndCallback(null)
        }
    }

    fun interface Callback {
        fun onResult(content: String?)
    }

    companion object {
        private const val FRAGMENT_TAG = "any_unique_tag"
        private const val REQUEST_CODE = 1501

        fun start(fragmentActivity: FragmentActivity?, callback: Callback) {
            if (fragmentActivity != null && !fragmentActivity.isFinishing) {
                val manager = fragmentActivity.supportFragmentManager
                val old = manager.findFragmentByTag(FRAGMENT_TAG)
                if (old != null) {
                    fragmentActivity.supportFragmentManager.beginTransaction().remove(old).commitNowAllowingStateLoss()
                }

                val fragment = ScanProxyFragment()
                fragment.callback = callback

                manager.beginTransaction().add(fragment, FRAGMENT_TAG).commitAllowingStateLoss()
            }
        }

    }

}

