package com.intl.base

import android.content.Context
import android.util.Log
import androidx.work.CoroutineWorker
import androidx.work.WorkerParameters
import com.google.gson.reflect.TypeToken
import com.intl.bu.model.api.ApiUploadFile
import kotlinx.coroutines.Dispatchers
import java.io.File
import kotlinx.coroutines.withContext
import java.io.IOException

class AudioUploadWorker(appContext: Context, workerParams: WorkerParameters) : CoroutineWorker(appContext, workerParams) {

    companion object {
        const val TAG = "AudioUploadWorker"
        const val KEY_URL = "url"
        const val KEY_HEADERS = "headers"
        const val KEY_BODY = "body"
        const val KEY_FILE_PATH = "file_path"
    }

    private val url: String?
    private val headers: Map<String, String>?
    private val body: Map<String, String>?
    private val filePath: String?

    init {
        Log.d(TAG, "init")
        // 获取参数
        url = inputData.getString(KEY_URL)

        val mapType = object : TypeToken<Map<String, String>>() {}.type
        val headersJson = inputData.getString(KEY_HEADERS)
        val bodyAsJson = inputData.getString(KEY_BODY)
        headers = headersJson?.let { Util.toObj(it, mapType) }
        body = bodyAsJson?.let { Util.toObj(it, mapType) }
        filePath = inputData.getString(KEY_FILE_PATH)
    }


    override suspend fun doWork(): Result {
        return withContext(Dispatchers.IO) { // 切换到 IO 调度器，适合文件操作
            try {
                Log.d(TAG, "doWork - Worker started")
                if (url.isNullOrEmpty() || headers.isNullOrEmpty() || filePath.isNullOrEmpty()) {
                    return@withContext Result.failure() // 参数不全，结束任务
                }

                // 执行文件上传操作
                val file = File(filePath)
                if (uploadFile(File(filePath))) {
                    Log.d(TAG, "文件上传成功: ${file.name}")
                    // 上传成功后删除文件
                    deleteFile(file)
                } else {
                    Log.d(TAG, "文件上传失败: ${file.name}")
                    return@withContext Result.failure() // 上传失败，返回失败
                }
            } catch (e: Exception) {
                Log.e(TAG, "doWork 中发生异常: ${e.message}")
                e.printStackTrace()
                return@withContext Result.failure() // 如果任务被中断或发生错误，返回失败
            }

            Log.d(TAG, "doWork - Worker完成")
            return@withContext Result.success() // 正常完成任务
        }
    }

    /**
     * 执行文件上传操作
     * @param file 要上传的文件
     */
    private fun uploadFile(file: File): Boolean {
        val sizeInKB = getFileSizeInKB(file)
        val startMs = System.currentTimeMillis()
        try {
            // 这里是上传的代码逻辑，例如使用 Retrofit 或其他网络库上传文件
            Log.d(TAG, "上传文件: ${file.name}")
            val out = Util.postFileSync(ApiUploadFile(url!!), headers!!, body!!, ApiUploadFile.In.formFieldName, file)
            val durationInSec = (System.currentTimeMillis() - startMs) / 1000
            val success = out.isSuccess()
            trackUploadResult(sizeInKB, success, durationInSec, out.resultMessage ?: "")
            return success
        } catch (e: IOException) {
            val durationInSec = (System.currentTimeMillis() - startMs) / 1000
            trackUploadResult(sizeInKB, false, durationInSec, e.toString())
            return false
        } catch (e: Exception) {
            val durationInSec = (System.currentTimeMillis() - startMs) / 1000
            trackUploadResult(sizeInKB, false, durationInSec, e.toString())
            return false
        }
    }

    /**
     * 删除上传后的文件
     * @param file 要删除的文件
     */
    private fun deleteFile(file: File) {
        if (file.delete()) {
            Log.d(TAG, "文件删除成功: ${file.name}")
        } else {
            Log.e(TAG, "文件删除失败: ${file.name}")
        }
    }

    private fun getFileSizeInKB(file: File): Long {
        // 获取文件大小，单位是字节
        val fileSizeInBytes = file.length()
        // 转换为 KB
        return fileSizeInBytes / 1024
    }

    private fun trackUploadResult(sizeInKB: Long, success: Boolean, durationInSec: Long, message: String = "") {
        try {
            if (message.contains("example.com")) {
                // 测试页面test.html产生的数据，不上报
                return
            }

            val map = HashMap<String, Any>()
            map["sizeInKB"] = sizeInKB
            map["success"] = success
            map["durationInSec"] = durationInSec
            map["message"] = message
            SensorsDataUtil.appAuth("debug", "debug_upload_file_result", "上传文件结果", map)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

}


