package com.intl.bu.activity

import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import com.intl.BuildConfig
import com.intl.ConstByFlavor
import com.intl.R
import com.intl.base.Constant
import com.intl.base.UniqueKeys
import com.intl.base.Util
import com.intl.bu.model.Headers
import com.intl.bu.model.api.ApiTrackPushEvent
import com.intl.lib_common.base.BaseActivity
import com.intl.lib_common.util.PrefUtil
import java.io.IOException


class SplashActivity : BaseActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        /**
         * fix bug:
         * 通过三方浏览器等安装app后直接启动进入某页面，然后按HOME退出后，
         * 再从桌面进入时，此时不会直接回到之前的页面
         *
         * 参考：
         * https://www.jianshu.com/p/24f2ed851f15
         * https://www.jianshu.com/p/1f0fad801d1c
         * https://www.jianshu.com/p/62ee47a659f1
         */
        if (!isTaskRoot && intent.hasCategory(Intent.CATEGORY_LAUNCHER) && intent.action == Intent.ACTION_MAIN) {
            finish()
            return
        }

        setContentView(R.layout.splash_activity)


        gotoMainActivitySoon()
        trackPushClickIfAny()
    }

    private fun gotoMainActivitySoon() {
        Handler(Looper.myLooper()!!).postDelayed(
            {
                val intent = Intent(this, MainActivity::class.java)
                intent.setFlags(Util.FLAG_CLEAR_TOP)
                startActivity(intent)

                finish()
            }, 1500
        )
    }

    private fun trackPushClickIfAny() {
        val pushId = intent.getStringExtra(Constant.KEY_PUSH_ID)
        if (pushId?.isNotEmpty() == true) {
            val loginToken = PrefUtil.getString(this, UniqueKeys.USER_TOKEN)
            if (loginToken?.isNotEmpty() == true) {
                val baseUrl = Util.getBaseUrl(this)
                Util.post(
                    ApiTrackPushEvent("$baseUrl/${ConstByFlavor.API_PATH_TRACK_PUSH_EVENT}"),
                    Headers.build(loginToken),
                    ApiTrackPushEvent.In(eventType = ApiTrackPushEvent.CLICK, flowNo = pushId),
                    object : Util.TypeCallback<ApiTrackPushEvent.Out> {
                        override fun onResponse(httpSuccess: Boolean, responseBody: ApiTrackPushEvent.Out?) {
                        }

                        override fun onFailure(e: IOException) {
                        }
                    }
                )
            } else {
                // 暂时不支持非登录态埋点
            }
        }
    }
}