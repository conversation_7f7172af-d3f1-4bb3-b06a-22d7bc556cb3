package com.intl.bu.activity

import android.content.DialogInterface
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.util.TypedValue
import android.view.View
import android.widget.Button
import android.widget.LinearLayout
import android.widget.TextView
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.widget.AppCompatEditText
import com.google.firebase.messaging.FirebaseMessaging
import com.intl.BuildConfig
import com.intl.R
import com.intl.base.TempLog
import com.intl.base.UniqueKeys
import com.intl.base.Util
import com.intl.lib_common.base.BaseActivity
import com.intl.lib_common.util.PrefUtil
import com.xinye.xmp.XMP
import com.xinye.xmp.utils.ToastUtil

class DevToolActivity : BaseActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setContentView(R.layout.activity_dev_tool)

        val apkInfo = """
            buildType=${BuildConfig.BUILD_TYPE}
            versionName=${BuildConfig.VERSION_NAME}
            versionCode=${BuildConfig.VERSION_CODE}
            last commit=${BuildConfig.COMMIT_HASH}
            buildTime=${BuildConfig.BUILD_TIME}
            log=${TempLog.get()}
        """.trimIndent()
        addText(apkInfo) {}

        if (BuildConfig.DEBUG) {
            addButton("switch api base url") {
                chooseServer()
            }

            addButton("copy google push token") {
                copyGmsToken()
            }

            addButton("open js-call-native test page") {
                XMP.enter(this, "${Util.getBaseUrl(this)}/demo/bridge", null)
            }

            addButton("H5测试页面") {
                XMP.enter(this@DevToolActivity, "file:///android_asset/test.html", null)
            }
        }
    }

    fun addText(name: String, click: View.OnClickListener) {
        val tv = TextView(this)
        tv.setTextSize(TypedValue.COMPLEX_UNIT_SP, 14f);
        tv.isAllCaps = false
        val lp = LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT)
        lp.topMargin = 20
        tv.layoutParams = lp
        tv.text = name
        tv.setOnClickListener(click)

        val ll = findViewById<LinearLayout>(R.id.ll)
        ll.addView(tv)
    }

    fun addButton(name: String, click: View.OnClickListener) {
        val tv = Button(this)
        tv.setTextSize(TypedValue.COMPLEX_UNIT_SP, 18f);
        tv.isAllCaps = false
        val lp = LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT)
        lp.topMargin = 20
        tv.layoutParams = lp
        tv.text = name
        tv.setOnClickListener(click)

        val ll = findViewById<LinearLayout>(R.id.ll)
        ll.addView(tv)
    }


    private fun copyGmsToken() {
        FirebaseMessaging.getInstance().token.addOnCompleteListener { task ->
            if (task.isSuccessful) {
                val token = task.result
                Util.copyToClip(this@DevToolActivity, token, "copied")
            } else {
                Util.toast(this@DevToolActivity, "fail")
            }
        }
    }

    private fun chooseServer() {
        val list = arrayOf(
            "http://*************:5173",
            "http://*************:5173",
            "http://fat-h5.yessscredit.id",
            "http://h5.yessscredit.id",
        )
        val editText = AppCompatEditText(this)
        editText.hint = "choose or input url"
        editText.setSingleLine()
        editText.requestFocus()
        AlertDialog.Builder(this)
            .setView(editText)
            //.setIcon(R.mipmap.ic_launcher)
            .setTitle("api base url")
            .setSingleChoiceItems(list, -1) { _: DialogInterface?, i: Int -> editText.setText(list[i]) }
            .setPositiveButton("ok") { _: DialogInterface?, _: Int ->
                val url = editText.text.toString()
                if (TextUtils.isEmpty(url) || !url.startsWith("http")) {
                    ToastUtil.showToast("invalid url")
                    chooseServer()
                } else {
                    PrefUtil.putString(this, UniqueKeys.BASE_URL, url)
                    // restart
                    val i = Intent(this, SplashActivity::class.java)
                    val clearStack = Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK
                    i.setFlags(clearStack)
                    startActivity(i)
                }
            }
            .setNegativeButton("cancel", null)
            .create()
            .show()
    }
}