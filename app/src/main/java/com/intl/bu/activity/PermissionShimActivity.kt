package com.intl.bu.activity

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.util.Log
import com.intl.base.AppUsageUtil
import com.intl.lib_common.base.BaseActivity

/**
 * 有些权限，例如PACKAGE_USAGE_STATS，需要跳系统设置页授权，为了让调用方通过callback的方式拿到授权结果，我们设计一个透明的辅助Activity，用于监听授权结果
 */
class PermissionShimActivity : BaseActivity() {
    val permission: String?
        get() = intent.getStringExtra(EXTRA_KEY)


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        Log.d("mayqx", "ShimActivity.onCreate")

        when (permission) {
            android.Manifest.permission.PACKAGE_USAGE_STATS -> {
                AppUsageUtil.goSetting(this)
            }

            else -> {
                finish()
            }
        }
    }

    override fun onRestart() {
        super.onRestart()
        Log.d("mayqx", "ShimActivity.onRestart")

        when (permission) {
            android.Manifest.permission.PACKAGE_USAGE_STATS -> {
                val granted = AppUsageUtil.isAppUsageEnabled(this)
                callback?.invoke(granted)
            }

            else -> {
                callback?.invoke(false)
            }
        }

        finish()
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d("mayqx", "ShimActivity.onDestroy")
    }

    companion object {
        val EXTRA_KEY = "key1"

        var callback: ((Boolean) -> Unit)? = null
        fun start(context: Context, permission: String) {
            val i = Intent(context, PermissionShimActivity::class.java)
            i.putExtra(EXTRA_KEY, permission)
            context.startActivity(i)
        }

    }
}