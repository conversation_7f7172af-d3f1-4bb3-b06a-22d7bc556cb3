package com.intl.bu.activity

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.provider.ContactsContract
import com.intl.base.ContactData
import com.intl.lib_common.base.BaseActivity

/**
 * 辅助选择联系人
 */
class ContactShimActivity : BaseActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        startIntent()
    }

    private fun startIntent() {
        val intent = Intent(Intent.ACTION_PICK)
        intent.setDataAndType(
            ContactsContract.Contacts.CONTENT_URI,
            ContactsContract.CommonDataKinds.Phone.CONTENT_TYPE //必要的
        )
        startActivityForResult(intent, 100)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        if (requestCode == 100 && resultCode == Activity.RESULT_OK) {
            data?.data?.let { contactUri ->
                val contact = getContactDetail(contactUri)
                callback?.invoke(contact)
            }
        }

        finish()
    }

    @SuppressLint("Range")
    private fun getContactDetail(contactUri: Uri): ContactData {
        try {
            var name: String? = null
            var number: String? = null
            val cursor = contentResolver.query(
                contactUri,
                arrayOf(
                    ContactsContract.CommonDataKinds.Phone.DISPLAY_NAME,
                    ContactsContract.CommonDataKinds.Phone.NUMBER,
                ),
                null,
                null,
                null
            )

            cursor?.use {
                if (it.moveToFirst()) {
                    name = it.getString(it.getColumnIndex(ContactsContract.CommonDataKinds.Phone.DISPLAY_NAME))
                    number = it.getString(it.getColumnIndex(ContactsContract.CommonDataKinds.Phone.NUMBER))
                    it.close()
                }
            }

            return ContactData(name = name ?: "", mobile = number ?: "")
        } catch (e: Exception) {
            e.printStackTrace()
            return ContactData("", "")
        }
    }

    companion object {
        var callback: ((contact: ContactData) -> Unit)? = null
        fun start(context: Context) {
            val i = Intent(context, ContactShimActivity::class.java)
            context.startActivity(i)
        }

    }
}