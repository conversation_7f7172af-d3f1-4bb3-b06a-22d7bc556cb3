package com.intl.bu.view

import android.content.Context
import android.util.AttributeSet
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import com.intl.R
import androidx.core.graphics.toColorInt

class SalesTabView(context: Context, attrs: AttributeSet?) : FrameLayout(context, attrs) {
    private var currentSelectedIndex = 0

    private var textList = emptyList<String>()
    private var onImageResList = emptyList<Int>()
    private var offImageResList = emptyList<Int>()
    private var enabled = true
    private var visibleState = mutableListOf(true, true, true, true, true)

    private var listener: OnTabSelectListener? = null

    private val colorNml = "#808080".toColorInt()
    private val colorHtl = "#20B261".toColorInt()

    init {
        inflate(context, R.layout.sales_tab_view, this)
    }

    fun init(text: List<String>, on: List<Int>, off: List<Int>): SalesTabView {
        textList = text
        onImageResList = on
        offImageResList = off

        update()

        return this
    }

    private fun update() {
        textList.forEachIndexed { index, s ->
            val vg = findTabView(index)
            val tv = findTextView(index)
            val iv = findImageView(index)

            tv.text = textList[index]
            tv.setTextColor(if (index == currentSelectedIndex) colorHtl else colorNml)

            iv.setImageResource(if (index == currentSelectedIndex) onImageResList[index] else offImageResList[index])

            vg.setOnClickListener {
                if (!enabled) {
                    return@setOnClickListener
                }
                currentSelectedIndex = index
                update()
                listener?.onTabSelect(index)
            }

            vg.visibility = if (visibleState[index]) VISIBLE else GONE
        }
    }

    private fun findTabView(index: Int): ViewGroup {
        val root = getChildAt(0) as ViewGroup
        return root.getChildAt(index) as ViewGroup
    }

    private fun findImageView(index: Int): ImageView {
        val tabView = findTabView(index)
        return tabView.getChildAt(0) as ImageView
    }

    private fun findTextView(index: Int): TextView {
        val tabView = findTabView(index)
        return tabView.getChildAt(1) as TextView
    }


    fun setListener(l: OnTabSelectListener) {
        listener = l
    }

    fun checkTab(index: Int) {
        currentSelectedIndex = index
        update()
    }

    fun setEnableState(enable: Boolean) {
        enabled = enable
        update()
    }

    fun setTabVisibleState(state: List<Boolean>) {
        visibleState = state.toMutableList()
        update()
    }

    fun interface OnTabSelectListener {
        fun onTabSelect(index: Int)
    }

}