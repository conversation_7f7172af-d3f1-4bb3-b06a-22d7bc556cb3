package com.intl.bu.model

/**
.addUnsafe<PERSON><PERSON><PERSON><PERSON><PERSON>("X-GW-PRODUCT", "APP")
.addUnsafeNon<PERSON>cii("X-GW-TENANTID", "1")
.addUnsafeNon<PERSON>cii("X-GW-DEVICEID", Device.getAndroidId(context))
.addUnsafeNon<PERSON><PERSON>i("X-GW-PLATFORM", "Android")
.addUnsafeNon<PERSON>cii("X-GW-APPID", Secret.APP_ID)
.addUnsafeNonAscii("X-GW-NONCESTR", requestId)
.addUnsafeNonAscii("X-GW-TOKEN", model.token)
 */
enum class HeaderName(val value: String) {
    _1_PRODUCT("X-GW-PRODUCT"),
    _2_TENANTID("X-GW-TENANTID"),
    _3_DEVCIEID("X-GW-DEVICEID"),
    _4_PLATFORM("X-GW-PLATFORM"),
    _5_APPID("X-GW-APPID"),
    _6_NONCESTR("X-GW-NONCESTR"),
    _7_TOKEN("X-GW-TOKEN"),
    _8_CLIENT("X-CLIENT"),
    _9_OS("X-MF-OS"),
    _10_APPVERSION("X-MF-APPVERSION"),
    ;
}
