package com.intl.bu.model.api

class ApiTrackPushEvent(private val url: String) : BaseApi<ApiTrackPushEvent.Out>() {
    companion object {
        val ARRIVE = "PUSH_MESSAGE_MARK_ARRIVE_EVENT"
        val CLICK = "PUSH_MESSAGE_MARK_CLICK_EVENT"
    }

    data class In(
        val eventType: String?,
        val flowNo: String?,
    )

    class Out() : BaseOut()

    override fun url(): String {
        return url
    }

    override fun outClass(): Class<Out> {
        return Out::class.java
    }
}
