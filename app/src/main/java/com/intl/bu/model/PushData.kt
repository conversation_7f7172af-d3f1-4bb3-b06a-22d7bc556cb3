package com.intl.bu.model

import org.json.JSONException
import org.json.JSONObject
import java.io.Serializable

class PushData(map: Map<String?, String?>) : Serializable {
    var title: String?
    var body: String?
    var flowNo: String?
    var extText: Ext? = null

    init {
        title = map["title"] // 注意这些key不要随便改!
        body = map["body"]
        flowNo = map["flowNo"]
        try {
            map["extText"]?.let {
                extText = Ext(JSONObject(it))
            }
        } catch (e: JSONException) {
            e.printStackTrace()
        }
    }

    class Ext(map: JSONObject) : Serializable {
        var url: String

        init {
            url = map.optString("url")
        }
    }
}
