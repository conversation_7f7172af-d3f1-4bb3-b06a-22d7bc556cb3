package com.intl.bu.model.api

class ApiUploadPrivacyData(private val url: String) : BaseApi<ApiUploadPrivacyData.Out>() {
    data class In(
        val dataType: String?,
        val platformType: String?,
        val collectionContent: String?,
        val osType: String?,
        val encrypt: String?,
    )

    class Out() : BaseOut()

    override fun url(): String {
        return url
    }

    override fun outClass(): Class<Out> {
        return Out::class.java
    }

    companion object {
        fun decodeSdkData(map: Map<String, String>?): In {
            return ApiUploadPrivacyData.In(
                map?.get("AcType"),
                map?.get("SourceType"),
                map?.get("appdata"),
                map?.get("OS"),
                map?.get("secretKey"),
            )
        }
    }
}
