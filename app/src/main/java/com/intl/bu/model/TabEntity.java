package com.intl.bu.model;

import com.flyco.tablayout.listener.CustomTabEntity;

public class TabEntity implements CustomTabEntity {
    public String title;
    public int selectedIcon;
    public int unSelectedIcon;

    public TabEntity(String stitle, int sSelectedIcon, int sUnSelectedIcon) {
        this.title = stitle;
        this.selectedIcon = sSelectedIcon;
        this.unSelectedIcon = sUnSelectedIcon;
    }

    @Override
    public String getTabTitle() {
        return title;
    }

    @Override
    public int getTabSelectedIcon() {
        return selectedIcon;
    }

    @Override
    public int getTabUnselectedIcon() {
        return unSelectedIcon;
    }
}
