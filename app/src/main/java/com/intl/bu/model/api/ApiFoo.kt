package com.intl.bu.model.api

class ApiFoo : BaseApi<ApiFoo.Out>() {
    data class In(val mobile: String)

    class Out(val content: Content?) : BaseOut()

    class Content(val targetXWidthOri: Int)

    override fun url(): String {
        return "https://lite.adakami.id/gateway/h5anon/user/h5/verificationCode/slider/info"
    }

    override fun outClass(): Class<Out> {
        return Out::class.java
    }

}
