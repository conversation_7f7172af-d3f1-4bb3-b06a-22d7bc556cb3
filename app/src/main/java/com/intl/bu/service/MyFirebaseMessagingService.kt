package com.intl.bu.service

import com.intl.ConstByFlavor
import android.app.PendingIntent
import android.content.Intent
import android.util.Log
import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage
import com.intl.base.Constant
import com.intl.base.NotificationUtil
import com.intl.base.UniqueKeys
import com.intl.base.Util
import com.intl.bu.activity.SplashActivity
import com.intl.bu.model.Headers
import com.intl.bu.model.PushData
import com.intl.bu.model.api.ApiTrackPushEvent
import com.intl.lib_common.util.PrefUtil
import java.io.IOException

class MyFirebaseMessagingService : FirebaseMessagingService() {
    private val TAG = "push"

    override fun onCreate() {
        super.onCreate()
        NotificationUtil.createNotificationChannel(this)
    }

    /**
     *
     * 注册令牌可能会在发生下列情况时更改：
     * 应用在新设备上恢复
     * 用户卸载/重新安装应用
     * 用户清除应用数据
     *
     * 如果你只是想查询当前的token，可调用FirebaseMessaging.getInstance().token.addOnCompleteListener
     */
    override fun onNewToken(token: String) {
        super.onNewToken(token)
        Log.d("mayqx", "onNewToken(), token=$token")
    }

    override fun onMessageReceived(remoteMessage: RemoteMessage) {
        // (developer): Handle FCM messages here.
        // Not getting messages here? See why this may be: https://goo.gl/39bRNJ
        Log.d(TAG, "From: ${remoteMessage.from}")

        // Check if message contains a data payload.
        if (remoteMessage.data.isNotEmpty()) {
            Log.d(TAG, "Message data payload: ${remoteMessage.data}")
        }

        // Check if message contains a notification payload.
        remoteMessage.notification?.let {
            Log.d(TAG, "Message Notification Body: ${it.body}")
        }

        val obj = PushData(remoteMessage.data)
        trackPushArrive(obj)

        // Also if you intend on generating your own notifications as a result of a received FCM
        // message, here is where that should be initiated. See sendNotification method below.

        // 暂时简单处理，总是跳启动页
        val intent = Intent(this, SplashActivity::class.java)
        intent.flags = Util.FLAG_CLEAR_TOP
        intent.putExtra(Constant.KEY_PUSH_ID, obj.flowNo) //用于点击埋点

        val pendingIntent = PendingIntent.getActivity(
            this,
            0,
            intent,
            (PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE /*android12+要求 */)
        )

        NotificationUtil.showNotification(
            this, obj.title, obj.body, pendingIntent
        )
    }

    /**
     * user和sales调不同的接口
     */
    fun trackPushArrive(data: PushData) {
        val loginToken = PrefUtil.getString(this, UniqueKeys.USER_TOKEN)
        if (loginToken?.isNotEmpty() == true) {
            val baseUrl = Util.getBaseUrl(this)
            Util.post(
                ApiTrackPushEvent("$baseUrl/${ConstByFlavor.API_PATH_TRACK_PUSH_EVENT}"),
                Headers.build(loginToken),
                ApiTrackPushEvent.In(eventType = ApiTrackPushEvent.ARRIVE, flowNo = data.flowNo),
                object : Util.TypeCallback<ApiTrackPushEvent.Out> {
                    override fun onResponse(httpSuccess: Boolean, responseBody: ApiTrackPushEvent.Out?) {
                    }

                    override fun onFailure(e: IOException) {
                    }
                }
            )
        } else {
            // 暂时不支持非登录态埋点
        }
    }

}