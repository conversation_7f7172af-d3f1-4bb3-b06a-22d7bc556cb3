package com.intl.bu.service

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Intent
import android.os.Build
import android.os.IBinder
import androidx.core.app.NotificationCompat
import com.intl.BuildConfig
import com.intl.R
import com.intl.bu.activity.DevToolActivity

/**
 * 参考 https://blog.51cto.com/u_16213344/9773361
 *
 * 由于这个方案需要用户打开“通知”权限，造成debug包和rls的差异，所以放弃；
 */
class DevToolService : Service() {
    private val CHANNEL_ID = "id_dev_tool"
    private val CHANNEL_NAME = "name_dev_tool"

    private var mNotificationManager: NotificationManager? = null
    override fun onBind(intent: Intent): IBinder? {
        return null
    }

    override fun onCreate() {
        super.onCreate()
        mNotificationManager = getSystemService(NOTIFICATION_SERVICE) as NotificationManager
        showNotification()
    }

    private fun showNotification() {
        // 创建通知渠道
        createNotificationChannel()

        // 创建intent
        val intent = Intent(this, DevToolActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            this,
            0,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT
                or PendingIntent.FLAG_IMMUTABLE //android12+要求
        )

        // 构建通知
        val notification = NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("${getString(R.string.app_name)} backdoor")
            .setContentText("commit=${BuildConfig.COMMIT_HASH}\nbuild=${BuildConfig.BUILD_TIME}")
            .setSmallIcon(R.drawable.notification)
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setContentIntent(pendingIntent)
            .setAutoCancel(false)
            .build()

        // 显示通知
        mNotificationManager!!.notify(1, notification)
    }

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(CHANNEL_ID, CHANNEL_NAME, NotificationManager.IMPORTANCE_DEFAULT)
            mNotificationManager!!.createNotificationChannel(channel)
        }
    }
}