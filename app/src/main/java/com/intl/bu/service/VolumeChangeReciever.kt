package com.intl.bu.service

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.media.AudioManager
import android.util.Log
import com.intl.bu.activity.DevToolActivity

/**
 * 当用户将音量从n减到0，打开调试后门
 */
class VolumeChangeReceiver : BroadcastReceiver() {
    override fun onReceive(context: Context?, intent: Intent?) {
        try {
            if (intent?.action == "android.media.VOLUME_CHANGED_ACTION") {
                val audioManager = context?.getSystemService(Context.AUDIO_SERVICE) as AudioManager
                val maxVolume = audioManager.getStreamMaxVolume(AudioManager.STREAM_MUSIC)

                val newVolume = intent.getIntExtra("android.media.EXTRA_VOLUME_STREAM_VALUE", -1)
                val oldVolume = intent.getIntExtra("android.media.EXTRA_PREV_VOLUME_STREAM_VALUE", -1)

                //Log.d("mayqx", "max=$maxVolume, $oldVolume -> $newVolume")
                if (newVolume == maxVolume) {
                    val i = Intent(context, DevToolActivity::class.java)
                    i.flags = Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK
                    context?.startActivity(i)
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}
