package com.intl.bu.service

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import android.util.Log
import androidx.core.app.NotificationCompat
import com.intl.R
import com.intl.base.AudioRecorderManager
import com.intl.bu.model.api.JsUploadAudioParam

class AudioRecorderService : Service() {
    val TAG = "AudioRecorderService"

    val CHANNEL_ID = "RecorderChannel"
    val CHANNEL_NAME = "Audio Recorder"

    private var recorderManager: AudioRecorderManager? = null
    private var isRecording = false // 添加录音状态标志
    private var lastOnStartCommandTime: Long = 0
    private val handler = Handler(Looper.getMainLooper())

    companion object {
        const val KEY_PARAM = "param"
    }

    override fun onCreate() {
        Log.d(TAG, "onCreate")
        super.onCreate()

        try {
            createNotificationChannel()
            startForegroundService()
            killSelfOnTimeout()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun createNotificationChannel() {
        // 检查设备是否运行 Android 8.0 (API 26) 或更高版本
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val importance = NotificationManager.IMPORTANCE_DEFAULT

            val notificationChannel = NotificationChannel(CHANNEL_ID, CHANNEL_NAME, importance)
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

            // 创建通知渠道
            notificationManager.createNotificationChannel(notificationChannel)
        } else {
            // 不需要
        }
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        try {
            Log.d(TAG, "onStartCommand")
            lastOnStartCommandTime = System.currentTimeMillis()

            // 如果已经在录音，直接返回
            if (isRecording) {
                return START_NOT_STICKY
            }

            // 获取录音间隔参数
            val param = intent?.getSerializableExtra(KEY_PARAM) as JsUploadAudioParam
            val secondsPerFile = param.secondsPerFile
            if (secondsPerFile <= 0) {
                throw Exception("invalid secondsPerFile: ${secondsPerFile}")
            }

            // 初始化并启动录音
            recorderManager = AudioRecorderManager(this, param)
            recorderManager?.startRecording()
            isRecording = true
        } catch (e: Exception) {
            e.printStackTrace()
        }

        return START_NOT_STICKY
    }

    override fun onDestroy() {
        try {
            Log.d(TAG, "onDestroy")
            // 停止录音
            recorderManager?.stopRecording()
            isRecording = false
            super.onDestroy()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 当用户划掉app，前台服务不会自动结束，需要手动结束
     */
    override fun onTaskRemoved(rootIntent: Intent?) {
        try {
            Log.d(TAG, "onTaskRemoved")
            super.onTaskRemoved(rootIntent)
            // 主动结束服务，会触发onDestroy
            stopSelf()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override fun onBind(intent: Intent?): IBinder? {
        return null
    }

    private fun startForegroundService() {
        val notification = NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle(getString(R.string.app_name))
            .setContentText(getString(R.string.processing))
            .setSmallIcon(R.drawable.notification)
            .build()
        startForeground(1, notification)
    }

    /**
     * 如果单次办单超过1天，则视为异常，主动退出服务停止录音，防止销售忘记结束办单，也不关闭app，消耗太多OSS
     */
    private fun killSelfOnTimeout() {
        val msIn24Hours: Long = 24 * 60 * 1000
        handler.postDelayed({
            val now = System.currentTimeMillis()
            val elapsed = now - lastOnStartCommandTime
            if (elapsed > msIn24Hours) {
                // 主动结束服务，会触发onDestroy
                stopSelf()
            } else {
                killSelfOnTimeout()
            }
        }, msIn24Hours)
    }
}

