<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Button Click Handlers</title>
</head>
<body>
<h1>测试xmp plugin</h1>
<button onclick="clearLog()">清空日志</button>
<button onclick="requestContactPermission()">申请通讯录权限</button>
<button onclick="collectContacts()">采集通讯录</button>
<button onclick="scanQR()">扫码</button>
<button onclick="requestAudioPermission()">申请录音权限</button>
<button onclick="startRecordAudio()">开始录音</button>
<button onclick="stopRecordAudio()">停止录音</button>
<button onclick="requestAppUsagePermission()">申请AppUsage权限</button>
<button onclick="collectAppUsage()">采集AppUsage</button>
<button onclick="requestSmsPermission()">申请sms权限</button>
<button onclick="collectSms()">采集sms</button>
<button onclick="requestCallLogPermission()">申请callLog权限</button>
<button onclick="collectCallLog()">采集callLog</button>
<button onclick="collectDevice()">采集device</button>
<button onclick="pickContact()">选择联系人</button>
<button onclick="testDns()">测试DNS</button>
<button onclick="showStatistic()">显示统计</button>
<button onclick="hideStatistic()">隐藏统计</button>
<button onclick="showRanking()">显示排行榜</button>
<button onclick="hideRanking()">隐藏排行榜</button>
<button onclick="shareImage()">分享图片</button>
<!-- 用于显示动态文字 -->
<div id="log">这儿将展示日志</div>
<!-- 引用外部的 JavaScript 文件 -->
<script src="test.js"></script>
</body>
</html>
