window.onNativeCall = function(data) {
    const ele = document.getElementById("log");
    ele.innerText += data + '\n';
}

function clearLog() {
   const ele = document.getElementById("log");
   ele.innerText = ""
}

function requestContactPermission() {
  const data = {
    "method": "request_permission",
    "callbackId": "123",
    "data": {
        "type": "contact",
    }
  }

  const jsonString = JSON.stringify(data);
  window.xmp.exec(jsonString);
}

function collectContacts() {
  const data = {
    "method": "collect_privacy_data",
    "callbackId": "123",
    "data": {
        "type": "contact",
        "url": "https://example.com",
        "headers": {
        }
    }
  }

  const jsonString = JSON.stringify(data);
  window.xmp.exec(jsonString);
}

function scanQR() {
    const data = {
      "method": "scan",
      "callbackId": "123",
      "data": {
      }
    }

    const jsonString = JSON.stringify(data);
    window.xmp.exec(jsonString);
}

function requestAudioPermission() {
  const data = {
    "method": "request_permission",
    "callbackId": "123",
    "data": {
        "type": "audio",
    }
  }

  const jsonString = JSON.stringify(data);
  window.xmp.exec(jsonString);
}

function startRecordAudio() {
  const data = {
    "method": "start_record_audio",
    "callbackId": "123",
    "data": {
        "secondsPerFile": "10",
        "url": "https://example.com",
        "headers": {
            "foo": "foo"
        },
        "body": {
            "foo": Math.random()
        }
    }
  }

  const jsonString = JSON.stringify(data);
  window.xmp.exec(jsonString);
}

function stopRecordAudio() {
  const data = {
    "method": "stop_record_audio",
    "callbackId": "123",
    "data": {
    }
  }

  const jsonString = JSON.stringify(data);
  window.xmp.exec(jsonString);
}

function requestAppUsagePermission() {
  const data = {
    "method": "request_permission",
    "callbackId": "123",
    "data": {
        "type": "appUsage",
    }
  }

  const jsonString = JSON.stringify(data);
  window.xmp.exec(jsonString);
}

function collectAppUsage() {
  const data = {
    "method": "collect_privacy_data",
    "callbackId": "123",
    "data": {
        "type": "appUsage",
        "url": "https://example.com",
        "headers": {
        }
    }
  }

  const jsonString = JSON.stringify(data);
  window.xmp.exec(jsonString);
}

function requestSmsPermission() {
  const data = {
    "method": "request_permission",
    "callbackId": "123",
    "data": {
        "type": "sms",
    }
  }

  const jsonString = JSON.stringify(data);
  window.xmp.exec(jsonString);
}

function collectSms() {
  const data = {
    "method": "collect_privacy_data",
    "callbackId": "123",
    "data": {
        "type": "sms",
        "url": "https://example.com",
        "headers": {
        }
    }
  }

  const jsonString = JSON.stringify(data);
  window.xmp.exec(jsonString);
}

function requestCallLogPermission() {
  const data = {
    "method": "request_permission",
    "callbackId": "123",
    "data": {
        "type": "callLog",
    }
  }

  const jsonString = JSON.stringify(data);
  window.xmp.exec(jsonString);
}

function collectCallLog() {
  const data = {
    "method": "collect_privacy_data",
    "callbackId": "123",
    "data": {
        "type": "callLog",
        "url": "https://example.com",
        "headers": {
        }
    }
  }

  const jsonString = JSON.stringify(data);
  window.xmp.exec(jsonString);
}

function collectDevice() {
  const data = {
    "method": "collect_privacy_data",
    "callbackId": "123",
    "data": {
        "type": "device",
        "url": "https://example.com",
        "headers": {
        }
    }
  }

  const jsonString = JSON.stringify(data);
  window.xmp.exec(jsonString);
}

function pickContact() {
  const data = {
    "method": "pick_contact",
    "callbackId": "123",
    "data": {}
  }

  const jsonString = JSON.stringify(data);
  window.xmp.exec(jsonString);
}

function testDns() {
   testSingleDns("system", "h5.yessscredit.id")
   testSingleDns("system", "gateway.yessscredit.id")
   testSingleDns("system", "www.google.com")

   testSingleDns("okhttp", "h5.yessscredit.id")
   testSingleDns("okhttp", "gateway.yessscredit.id")
   testSingleDns("okhttp", "www.google.com")
}

function testSingleDns(method, host) {
  const data = {
    "method": "test_dns",
    "callbackId": "123",
    "data": {
        "method": method,
        "host": host
    }
  }

  const jsonString = JSON.stringify(data);
  window.xmp.exec(jsonString);
}

function showStatistic() {
  const data = {
    "method": "display_tab",
    "callbackId": "123",
    "data": {
        "index": 3,
        "show": true
    }
  }

  const jsonString = JSON.stringify(data);
  window.xmp.exec(jsonString);
}

function hideStatistic() {
  const data = {
    "method": "display_tab",
    "callbackId": "123",
    "data": {
        "index": 3,
        "show": false
    }
  }

  const jsonString = JSON.stringify(data);
  window.xmp.exec(jsonString);
}

function showRanking() {
  const data = {
    "method": "display_tab",
    "callbackId": "123",
    "data": {
        "index": 4,
        "show": true
    }
  }

  const jsonString = JSON.stringify(data);
  window.xmp.exec(jsonString);
}

function hideRanking() {
  const data = {
    "method": "display_tab",
    "callbackId": "123",
    "data": {
        "index": 4,
        "show": false
    }
  }

  const jsonString = JSON.stringify(data);
  window.xmp.exec(jsonString);
}

function shareImage() {
  const data = {
    "method": "share_image",
    "callbackId": "123",
    "data": {
        "base64": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEgAAABICAYAAABV7bNHAAAAAXNSR0IArs4c6QAAAARzQklUCAgICHwIZIgAAAReSURBVHic7ZyxctNMEMf/KzkTmgxmKGkulQsaqClwXiD+xAMYxQUt1FSmoYaWIlb8ABjzAtFX0NOkSMU1lEzM0MQTS0shZSDoJEvySb7I/pXSabX6z+7d6fYkYMuWLVvWB63rxmLitgG0ATyCRXdhcRsh7gEALFwgpBlC/glASsf7ui4/axNITNw2LOqBuAumLgiioAkJkA+E/yOALx1PandSQaUCiYnbRgvPwfQfgK5m8z7AJ1WLVYlAUbTgFYheIkqjKpEAfUIQvq9CKO0Ciak7rEmYJExvZO94qNOkNoHExO3CphFQuG/RjUTAR9LxfB3GLB1GxHQwhE2nWL84ACBg06mYDoY6jK0UQWLitmHTBPo7YF34cTTJsgZKCyQmrjAoarKQCPigrEilBLpF4lxTWqTCAt1Cca4pJVIhgW6xONdIBPxYOt4s7wXFRjEzhvFVEPGgkpvcAsXDZregQybSLTIFyJVi8STwtLRLJhL1R/6yZvkiKEqtZpHzmZYKFIejWNEdExFi6g6XNcpMsXjU+qbNJfOYIeD9rFEtO4Js66V2l8wiWpbJIDWCGjDnyUtmFKVHkI0umi8OAESrnilkpBilXtQ4oiVhJcoU24DOOUnA91Rppo4guxEz5mJY1FMeTmn9tEpfjIS4qzqcIpC6caNh6qoOp3XSojJHTIUg4mrvDRICiYn7qB6PjGS5QNjE6PlDIjiSAll0txZXTETx7AqBuP6KqCkonj0p0PUWlE1E8exaKquNwcJF8lCORhuCxALTfw8qUoxyl0QahI+AHVXNrJVoGvJP2GvbmXcDeThauyOqPmht+wFNRCXQJqZYKur1oOnRtxKbLLWTlWKd6WBvTuFDgHaLWeX5Lltn573jX3laJ/sgACD2AXKL3bgeHnzs399p2W/nxH0w3SlugTC3+FJ8PhpfLYLX35+Nf2S1Vs+DmPziN66eWJwvAF6AUUKcmOjaFzst+8uDj/37WU3VAoWcmA+YwE7Lfgugo9FkJ7aZilKgeG3W1+jIynSmgz0Q+toNE/qd6WAv7XT6qwbxJ+3OrMCcwocrpVUajDtRZ68mXaAFTmDUkF90tNJjO1Ug6XgzML+vxqHbQ/bbfIh3MCqK6idToG0U5VgPkj1vCEBW7omh5FswC/ioYj+MJZdA0vF8ML2p2hkTyb3kGn9m5FfmiaEUW5MO2MGG9UeFBJKON0PAB9ggkQpXNaTjyU0SqVTZZ5NEKl0X+0skX587WfB8HbZXKhxKx5PycHRQxxRgl60zEC61GyZc7rJ1lnZaS2VV9o6HVafcee/4Fxhj7YYZ46z1aW2lZ+l4vjwc7VcZTVeL4DWAc40mz2ObqWivzcfRtA/QO2iOqO/Pxj+uFsETAB9WSrfo2g9Xi+DJskX7qn9NIaIds/QcRb81Y3yVvdHjtNN1lX3q/LlJLJb1NN4kKjKaz+JPJ2UdvmWx1tp3vB9S3Pg9joULLHBS5LvSLVu2bGkqvwFvNYh6PzU7sAAAAABJRU5ErkJggg==",
    }
  }

  const jsonString = JSON.stringify(data);
  window.xmp.exec(jsonString);
}

