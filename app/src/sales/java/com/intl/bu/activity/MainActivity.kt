package com.intl.bu.activity

import android.content.Intent
import android.os.Bundle
import com.intl.BuildConfig
import com.intl.R
import com.intl.base.AudioRecordUtil
import com.intl.base.Constant
import com.intl.base.GlobalVar
import com.intl.base.UniqueKeys
import com.intl.base.Util
import com.intl.base.Util.log
import com.intl.databinding.ActivityMainBinding
import com.intl.lib_common.base.BaseActivity
import com.intl.lib_common.util.PrefUtil
import com.intl.lib_common.util.StatusBarUtil
import com.xinye.xmp.XMP
import com.xinye.xmp.bridge.XMPPlugin
import com.xinye.xmp.ui.XMPFragment
import com.xinye.xmp.ui.XMPPagerFragment
import com.xinye.xmp.ui.XMPWebView
import org.json.JSONObject

class MainActivity : BaseActivity() {
    var binding: ActivityMainBinding? = null
    private var mFragments = arrayOf<XMPFragment>()

    private val text = listOf("<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Saya")

    private val off = listOf(
        R.drawable.sales_tab1_off,
        R.drawable.sales_tab4_off,
        R.drawable.sales_tab5_off,
        R.drawable.sales_tab2_off,
        R.drawable.sales_tab3_off,
    )
    private val on = listOf(
        R.drawable.sales_tab1_on,
        R.drawable.sales_tab4_on,
        R.drawable.sales_tab5_on,
        R.drawable.sales_tab2_on,
        R.drawable.sales_tab3_on,
    )

    // 和js之间，按name沟通，这些值就是tab的name
    private val position = listOf(0, 3, 4, 1, 2)
    private var visibleState = mutableListOf(true, false, false, true, true)

    private fun name2index(name: Int): Int {
        return position.indexOf(name)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)
        binding = getBinding()
        StatusBarUtil.setTransparentStatusImmerse(this, true)
        initFragments()
        initTab()
        showFragment(0)
        initXmpPlugin()
        checkDeepLink(intent)
    }

    /**
     * 首页打开状态下，点击deeplink，会跑到这儿
     */
    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        val useThisIntent = intent
        checkDeepLink(useThisIntent)
    }

    private fun initFragments() {
        var base = Constant.rls
        if (BuildConfig.DEBUG) {
            base = Util.getBaseUrl(this)
        }

        val homeFragment = XMPPagerFragment()
        val homeBundle = Bundle()
        homeBundle.putString("url", "${base}/sales/")
        homeFragment.arguments = homeBundle

        val dataFragment = XMPPagerFragment()
        val dataBundle = Bundle()
        dataBundle.putString("url", "${base}/sales/performance/statistics")
        dataFragment.arguments = dataBundle

        val rankFragment = XMPPagerFragment()
        val rankBundle = Bundle()
        rankBundle.putString("url", "${base}/sales/performance/ranking")
        rankFragment.arguments = rankBundle

        val billFragment = XMPPagerFragment()
        val billBundle = Bundle()
        billBundle.putString("url", "${base}/sales/order")
        billFragment.arguments = billBundle

        val mineFragment = XMPPagerFragment()
        val mineBundle = Bundle()
        mineBundle.putString("url", "${base}/sales/home")
        mineFragment.arguments = mineBundle

        mFragments = arrayOf(homeFragment, rankFragment, dataFragment, billFragment, mineFragment)

        supportFragmentManager.beginTransaction().apply {
            mFragments.forEachIndexed { index, fragment ->
                add(R.id.fragment_stub, fragment, "tag_$index")
            }
            commit()
        }
    }

    private fun initTab() {
        binding?.tab?.init(text, on, off)?.setListener { index ->
            showFragment(index)
        }

        restoreVisibleState()
        binding?.tab?.setTabVisibleState(visibleState)
    }

    private fun updateVisibleState(index: Int, visible: Boolean) {
        visibleState[index] = visible
        val state = visibleState.joinToString(",")

        PrefUtil.putString(this, UniqueKeys.TAB_VISIBLE_STATE, state)
    }

    private fun restoreVisibleState() {
        val state = PrefUtil.getString(this, UniqueKeys.TAB_VISIBLE_STATE)
        if (state?.isNotEmpty() == true) {
            val array = state.split(",")
            if (array.size == text.size) {
                visibleState = array.map { it.toBoolean() }.toMutableList()
            } else {
                // 数量不匹配，用默认值
            }
        }
    }

    private fun showFragment(index: Int) {
        supportFragmentManager.beginTransaction().apply {
            mFragments.forEach { hide(it) } // 隐藏所有
            show(mFragments[index]) // 显示目标 Fragment
            commit()
        }
        // 我没有用viewpager，onShowInViewPager()不会触发，导致js无法收到onShow事件，所以我手动触发下
        mFragments[index].onShowInViewPager(false)
    }

    private fun initXmpPlugin() {
        XMP.addPlugin(object : XMPPlugin() {
            override fun exec(f: XMPFragment?, w: XMPWebView?, data: JSONObject?, cbk: Callback?) {
                log("js调安卓, $name")
                data class In(val index: Int/*name*/)

                val p = Util.toObj(data!!, In::class.java)

                val index = name2index(p.index) //js给的其实是name
                showFragment(index)
                binding?.tab?.checkTab(index)
                cbk?.complete(null)
            }

            override fun getName(): String {
                return "check_tab"
            }

        })

        XMP.addPlugin(object : XMPPlugin() {
            override fun exec(f: XMPFragment?, w: XMPWebView?, data: JSONObject?, cbk: Callback?) {
                log("js调安卓, $name")
                data class In(val enable: Boolean)

                val p = Util.toObj(data!!, In::class.java)

                binding?.tab?.setEnableState(p.enable)
                cbk?.complete(null)
            }

            override fun getName(): String {
                return "enable_tabs"
            }

        })

        XMP.addPlugin(object : XMPPlugin() {
            override fun exec(f: XMPFragment?, w: XMPWebView?, data: JSONObject?, cbk: Callback?) {
                log("js调安卓, $name")
                data class In(val index: Int, val show: Boolean)

                val p = Util.toObj(data!!, In::class.java)
                val index = name2index(p.index) //js给的其实是name
                updateVisibleState(index, p.show)
                binding?.tab?.setTabVisibleState(visibleState)
                cbk?.complete(null)
            }

            override fun getName(): String {
                return "display_tab"
            }

        })
    }

    private fun checkDeepLink(intent: Intent?) {
        intent?.let {
            val url = Util.getDeepLink(this, intent)
            if (url?.isNotEmpty() == true) {
                GlobalVar.deepLink = url
                showFragment(0)
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()

        // service和worker默认没有随着app的关闭结束，我们手动结束下
        AudioRecordUtil.stopRecordService(this)
        AudioRecordUtil.stopUploadTask(this)
    }

}
