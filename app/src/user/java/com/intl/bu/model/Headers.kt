package com.intl.bu.model

import android.content.Context
import com.intl.lib_common.util.DeviceUtil
import java.util.UUID
import com.intl.BuildConfig

object Headers {
    val map = mutableMapOf(
        HeaderName._1_PRODUCT to "CFCC",
        HeaderName._2_TENANTID to "1",
        HeaderName._3_DEVCIEID to "",
        HeaderName._4_PLATFORM to "h5",
        HeaderName._5_APPID to "900007",
        HeaderName._6_NONCESTR to "",
        HeaderName._7_TOKEN to "",
        //HeaderName._8_CLIENT to "h5",
        HeaderName._9_OS to "android",
        HeaderName._10_APPVERSION to BuildConfig.VERSION_NAME,
    )

    fun init(context: Context) {
        map[HeaderName._3_DEVCIEID] = DeviceUtil.getAndroidId(context)
    }

    fun build(userToken: String?): Map<String, String> {
        map[HeaderName._6_NONCESTR] = UUID.randomUUID().toString()
        map[HeaderName._7_TOKEN] = userToken ?: ""

        return map.mapKeys { it.key.value }
    }
}