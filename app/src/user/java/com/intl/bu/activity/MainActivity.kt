package com.intl.bu.activity

import android.content.Intent
import android.os.Bundle
import com.flyco.tablayout.listener.CustomTabEntity
import com.flyco.tablayout.listener.OnTabSelectListener
import com.intl.BuildConfig
import com.intl.R
import com.intl.base.Constant
import com.intl.base.GlobalVar
import com.intl.base.UniqueKeys
import com.intl.base.Util
import com.intl.base.Util.log
import com.intl.base.Util.toObj
import com.intl.bu.model.TabEntity
import com.intl.databinding.ActivityMainBinding
import com.intl.lib_common.base.BaseActivity
import com.intl.lib_common.util.PrefUtil
import com.intl.lib_common.util.StatusBarUtil
import com.xinye.xmp.XMP
import com.xinye.xmp.bridge.XMPPlugin
import com.xinye.xmp.ui.XMPFragment
import com.xinye.xmp.ui.XMPPagerFragment
import com.xinye.xmp.ui.XMPWebView
import org.json.JSONObject

class MainActivity : BaseActivity() {
    var binding: ActivityMainBinding? = null
    private var mFragments = arrayOf<XMPFragment>()
    private val mTitles = arrayOf("Beranda", "Saya")
    private val mIconUnselectIds = intArrayOf(
        R.drawable.user_tab1_off, R.drawable.user_tab2_off,
    )
    private val mIconSelectIds = intArrayOf(
        R.drawable.user_tab1_on,
        R.drawable.user_tab2_on
    )
    private val mTabEntities = ArrayList<CustomTabEntity>()


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)
        binding = getBinding()
        //binding?.viewPager?.setCanScroll(false)
        StatusBarUtil.setTransparentStatusImmerse(this, true)
        initXmpFragment()
        addAllFragments()
        initTab()
        showFragment(0)
        initXmpPlugin()
        checkDeepLink(intent)
    }

    /**
     * 首页打开状态下，点击deeplink，会跑到这儿
     */
    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        val useThisIntent = intent
        checkDeepLink(useThisIntent)
    }

    private fun addAllFragments() {
        supportFragmentManager.beginTransaction().apply {
            mFragments.forEachIndexed { index, fragment ->
                add(R.id.fragment_stub, fragment, "tag_$index")
            }
            commit()
        }
    }

    private fun showFragment(index: Int) {
        binding?.tab?.currentTab = index

        supportFragmentManager.beginTransaction().apply {
            mFragments.forEach { hide(it) } // 隐藏所有
            show(mFragments[index]) // 显示目标 Fragment
            commit()
        }
        // 我没有用viewpager，onShowInViewPager()不会触发，导致js无法收到onShow事件，所以我手动触发下
        mFragments[index].onShowInViewPager(false)
    }

    private fun initXmpFragment() {
        var base = Constant.rls
        if (BuildConfig.DEBUG) {
            base = PrefUtil.getString(this, UniqueKeys.BASE_URL) ?: Constant.fat
        }

        val homeFragment = XMPPagerFragment()
        val homeBundle = Bundle()
        homeBundle.putString("url", "${base}/user/quota")
        homeFragment.arguments = homeBundle

        val mineFragment = XMPPagerFragment()
        val mineBundle = Bundle()
        mineBundle.putString("url", "${base}/user/home")
        mineFragment.arguments = mineBundle

        mFragments = arrayOf(homeFragment, mineFragment)
    }

    private fun initTab() {
        for (i in mTitles.indices) {
            mTabEntities.add(
                TabEntity(
                    mTitles.get(i),
                    mIconSelectIds.get(i),
                    mIconUnselectIds.get(i)
                )
            )
        }
        binding?.tab?.setTabData(mTabEntities)
        binding?.tab?.setOnTabSelectListener(object : OnTabSelectListener {
            override fun onTabSelect(position: Int) {
                showFragment(position)
            }

            override fun onTabReselect(position: Int) {}
        })
    }

    private fun initXmpPlugin() {
        XMP.addPlugin(object : XMPPlugin() {
            override fun exec(f: XMPFragment?, w: XMPWebView?, data: JSONObject?, cbk: Callback?) {
                log("js调安卓, $name")
                data class In(val index: Int)

                val p = toObj(data!!, In::class.java)

                showFragment(p.index)
                cbk?.complete(null)
            }

            override fun getName(): String {
                return "check_tab"
            }

        })

        XMP.addPlugin(object : XMPPlugin() {
            override fun exec(f: XMPFragment?, w: XMPWebView?, data: JSONObject?, cbk: Callback?) {
                log("js调安卓, $name")
                data class In(val enable: Boolean)

                val p = toObj(data!!, In::class.java)

                binding?.tab?.setCanClick(p.enable)
                cbk?.complete(null)
            }

            override fun getName(): String {
                return "enable_tabs"
            }

        })
    }

    private fun checkDeepLink(intent: Intent?) {
        intent?.let {
            val url = Util.getDeepLink(this, intent)
            if (url?.isNotEmpty() == true) {
                GlobalVar.deepLink = url
                showFragment(0)
            }
        }
    }

}
