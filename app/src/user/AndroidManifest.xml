<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.CAMERA" />
    <!--    <uses-permission android:name="android.permission.READ_CONTACTS" />-->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <!--    <uses-permission android:name="android.permission.PACKAGE_USAGE_STATS" />-->

    <!--START-这些权限都是OJK不允许获取的，所以直接remove掉-->
    <uses-permission
        android:name="android.permission.READ_EXTERNAL_STORAGE"
        tools:node="remove" />
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        tools:node="remove" />
    <uses-permission
        android:name="android.permission.READ_PHONE_STATE"
        tools:node="remove" />
    <uses-permission
        android:name="android.permission.CALL_PHONE"
        tools:node="remove" />
    <uses-permission
        android:name="android.permission.READ_SMS"
        tools:node="remove" />
    <uses-permission
        android:name="android.permission.READ_CONTACTS"
        tools:node="remove" />
    <uses-permission
        android:name="android.permission.READ_CALL_LOG"
        tools:node="remove" />
    <uses-permission
        android:name="android.permission.WRITE_SETTINGS"
        tools:ignore="ProtectedPermissions"
        tools:node="remove" />
    <uses-permission
        android:name="android.permission.PACKAGE_USAGE_STATS"
        tools:ignore="ProtectedPermissions"
        tools:node="remove" />
    <!--    <uses-permission-->
    <!--        android:name="android.permission.ACCESS_FINE_LOCATION"-->
    <!--        tools:ignore="ProtectedPermissions"-->
    <!--        tools:node="remove" />-->
    <!--END-这些权限都是OJK不允许获取的，所以直接remove掉-->
</manifest>
