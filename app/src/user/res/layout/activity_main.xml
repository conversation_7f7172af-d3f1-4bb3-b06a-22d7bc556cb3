<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tl="http://schemas.android.com/apk/res-auto">

    <data>

    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <FrameLayout
                android:id="@+id/fragment_stub"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1" />

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/_EBEBEB" />

            <com.intl.base.CustomCommonTabLayout
                android:id="@+id/tab"
                android:layout_width="match_parent"
                android:layout_height="66dp"
                android:background="@color/white"
                tl:tl_textSelectColor="@color/primary"
                tl:tl_textUnselectColor="@color/_999999"
                tl:tl_textsize="11sp" />
        </LinearLayout>
    </RelativeLayout>
</layout>

