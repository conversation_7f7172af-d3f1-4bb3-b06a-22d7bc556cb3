package com.intl.lib_common.base

import android.R
import android.app.Activity
import android.app.Application
import android.content.Intent
import android.content.pm.ActivityInfo
import android.os.Build
import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import android.view.Window
import androidx.appcompat.app.AppCompatActivity
import androidx.databinding.DataBindingUtil
import androidx.databinding.ViewDataBinding
import com.intl.lib_common.util.StatusBarUtil

open class BaseActivity : AppCompatActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        hideActionBar() //全局隐藏ActionBar
        super.onCreate(savedInstanceState)
        //status bar 文字使用深色
        StatusBarUtil.setDarkFontStatus(this)

        // 8.0上仅全屏和非透明的页面可指定屏幕方向
        // 考虑到8.0的用户少，且会越来越少，我们简单处理，仅对非8.0指定屏幕方向
        if (Build.VERSION.SDK_INT != Build.VERSION_CODES.O) {
            requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT // 禁用横屏
        }
    }

    open fun <B : ViewDataBinding?> getBinding(): B? {
        val decorView = window.decorView
        val contentView = decorView.findViewById<View>(R.id.content) as ViewGroup
        return DataBindingUtil.bind<ViewDataBinding>(contentView.getChildAt(0)) as B?
    }

    fun hideActionBar() {
        try {
            requestWindowFeature(Window.FEATURE_NO_TITLE)
            supportActionBar!!.hide()
        } catch (e: Exception) {
        }
    }
}
