package com.intl.lib_common.activity

import com.intl.lib_common.R
import android.content.Intent
import android.graphics.BitmapFactory
import android.net.Uri
import android.widget.ImageView
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.lifecycle.lifecycleScope
import com.intl.lib_common.util.StatusBarUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import com.king.camera.scan.AnalyzeResult
import com.king.camera.scan.CameraScan
import com.king.camera.scan.analyze.Analyzer
import com.google.zxing.Result
import com.king.zxing.util.CodeUtils
import com.king.zxing.BarcodeCameraScanActivity
import com.king.zxing.DecodeConfig
import com.king.zxing.DecodeFormatManager
import com.king.zxing.analyze.MultiFormatAnalyzer
import java.io.InputStream


class CustomCaptureActivity : BarcodeCameraScanActivity() {
    private val PROCESSING = "Sedang diproses, harap tunggun"
    private val NO_QR_CODE_FOUND = "Kode QR tidak ditemukan"

    private val galleryLauncher = registerForActivityResult(
        ActivityResultContracts.GetContent()
    ) { uri: Uri? ->
        uri?.let { processImageFromGallery(it) }
    }

    override fun initUI() {
        super.initUI()

        StatusBarUtil.setDarkFontStatus(this)
        StatusBarUtil.setTransparentStatusImmerse(this, true)

        val back = findViewById<ImageView>(R.id.iv_back)
        back.setOnClickListener {
            val intent = Intent()
            intent.putExtra(CameraScan.SCAN_RESULT, "")
            setResult(RESULT_OK, intent)
            finish()
        }

        val album = findViewById<ImageView>(R.id.iv_album)
        album.setOnClickListener {
            openGallery()
        }
    }

    private fun openGallery() {
        galleryLauncher.launch("image/*")
    }

    private fun processImageFromGallery(uri: Uri) {
        // 显示加载提示
        Toast.makeText(this, PROCESSING, Toast.LENGTH_SHORT).show()

        lifecycleScope.launch {
            try {
                val qrResult = withContext(Dispatchers.IO) {
                    // 在IO线程中读取图片和识别二维码
                    val inputStream: InputStream? = contentResolver.openInputStream(uri)
                    val bitmap = BitmapFactory.decodeStream(inputStream)
                    inputStream?.close()

                    if (bitmap != null) {
                        CodeUtils.parseCode(bitmap)
                    } else {
                        null
                    }
                }

                // 回到主线程处理结果
                withContext(Dispatchers.Main) {
                    if (qrResult != null) {
                        // 返回二维码结果
                        val intent = Intent()
                        intent.putExtra(CameraScan.SCAN_RESULT, qrResult)
                        setResult(RESULT_OK, intent)
                        finish()
                    } else {
                        Toast.makeText(this@CustomCaptureActivity, NO_QR_CODE_FOUND, Toast.LENGTH_SHORT).show()
                    }
                }
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    Toast.makeText(this@CustomCaptureActivity, "${e.message}", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }

    override fun initCameraScan(cameraScan: CameraScan<Result>) {
        super.initCameraScan(cameraScan)
        // 根据需要设置CameraScan相关配置
        cameraScan.setPlayBeep(true)
    }

    override fun createAnalyzer(): Analyzer<Result> {
        // 初始化解码配置
        val decodeConfig = DecodeConfig()
        decodeConfig.setHints(DecodeFormatManager.QR_CODE_HINTS) //如果只有识别二维码的需求，这样设置效率会更高，不设置默认为DecodeFormatManager.DEFAULT_HINTS
            .setFullAreaScan(false) //设置是否全区域识别，默认false
            .setAreaRectRatio(0.8f) //设置识别区域比例，默认0.8，设置的比例最终会在预览区域裁剪基于此比例的一个矩形进行扫码识别
            .setAreaRectVerticalOffset(0) //设置识别区域垂直方向偏移量，默认为0，为0表示居中，可以为负数
            .setAreaRectHorizontalOffset(0) //设置识别区域水平方向偏移量，默认为0，为0表示居中，可以为负数
        // BarcodeCameraScanActivity默认使用的MultiFormatAnalyzer，如果只识别二维码，这里可以改为使用QRCodeAnalyzer
        return MultiFormatAnalyzer(decodeConfig)
    }

    /**
     * 布局ID；通过覆写此方法可以自定义布局
     *
     * @return 布局ID
     */
    override fun getLayoutId(): Int {
        return R.layout.custom_capture
    }

    override fun onScanResultCallback(result: AnalyzeResult<Result>) {
        // 停止分析
        cameraScan.setAnalyzeImage(false)
        // 返回结果
        val intent = Intent()
        intent.putExtra(CameraScan.SCAN_RESULT, result.getResult().getText())
        setResult(RESULT_OK, intent)
        finish()
    }
}
