package com.intl.lib_common.util

import android.annotation.SuppressLint
import android.content.Context
import android.provider.Settings
import com.google.android.gms.ads.identifier.AdvertisingIdClient

object DeviceUtil {
    private var mID = ""

    fun init(context: Context?) {
        getOrCreateAdid(context)
    }

    fun getAdid(context: Context?): String {
        getOrCreateAdid(context)
        return mID
    }

    private fun getOrCreateAdid(context: Context?): String {
        if (mID.isEmpty()) {
            Thread {
                try {
                    if (context != null) {
                        val info = AdvertisingIdClient.getAdvertisingIdInfo(context)
                        mID = info.id ?: ""
                    }
                } catch (e: Exception) {
                    // 从firebase看，有可能遇到IOException
                }
            }.start()
        }

        return mID
    }

    @SuppressLint("HardwareIds")
    fun getAndroidId(context: Context): String {
        return try {
            Settings.Secure.getString(
                context.contentResolver,
                Settings.Secure.ANDROID_ID
            )
        } catch (e: Exception) {
            ""
        }
    }
}
