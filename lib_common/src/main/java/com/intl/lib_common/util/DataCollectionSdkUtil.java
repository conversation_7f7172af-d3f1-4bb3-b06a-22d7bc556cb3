package com.intl.lib_common.util;

import android.content.Context;
import android.util.Log;

import androidx.annotation.NonNull;

import com.google.gson.Gson;
import com.ppdai.module.datacollection.AcType;
import com.ppdai.module.datacollection.DataCollection;
import com.ppdai.module.datacollection.JsonConverter;
import com.ppdai.module.datacollection.base.ResultCallback;
import com.ppdai.module.datacollection.base.UploadHandler;
import com.ppdai.module.datacollection.utils.DataConfig;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * 采集隐私数据
 * 参考： http://git.ppdaicorp.com/sdk/AndroidDataCollectionSDK
 */
public class DataCollectionSdkUtil {
    public static void init(Context mContext) {
        DataConfig.initApplicationcontext(mContext.getApplicationContext());
        // 设置 App 标识，目前各 App 的标识如下, 请各 App 自己填入对应的标识
        // 0 - 未知; 1 - 借入 2 - 借出 3 - 拍分期 4 - SDK 5 - 曹操贷;  6 - 还还;  7 - 万元贷;  8 - 白条 SDK
        DataConfig.setBusinessType("1"); //后端未使用，非空即可
        // 是否进行评估额度，一般的，借入类型的 App 都需要设置评估额度，其他类型的 App（如 借出）请和 PM 确认是否需要帮用户评估额度。
        DataConfig.setGrade(true);
        // 是否收集采集时间（采集时间是一个单独的接口，由移动提供，采集数据接口是实时数据提供）该接口只是用来统计用。
        DataConfig.setCollectionUploadTime(false);
        // 设置上传处理器，需要实现 UploadHandler 接口，网络处理就是在这个接口完成。
        // 换句话说，SDK 只关注数据的采集，不关心数据的上传，上传是各 App 端处理的，文档后面会给出 UploadHandler 的实现细节
        //DataConfig.setUploadhandler(handler); //为了拿到user token，延后到js调用的时候再设置
        // JSON 与 Java 对象转换接口。设计这个接口的原因是统一 JSON 的处理，避免 SDK 引用的 JSON 库与 App 的不同，增加大小。
        // 文档后面会给出 JsonConvert接口的实现细节
        DataConfig.setJsonConvert(new FastJsonConverter());
        // 设置每次上传图片数据的最大数量（因为现在是敏感时期，故设置 0 为不采集）
        DataConfig.setImageAttrUploadOnceCountLimit(0);
    }

    public static void collect(AcType type, UploadHandler uploadHandler) {
        DataConfig.setUploadhandler(uploadHandler);

        // AcType 是SDK采集数据的类型，这个方法只采集 AcType 指定的数据。
        // boolean 用来标记是否强制采集。 true 强制采集， false 非强制采集
        DataCollection.getInstance().perform(type, true);
    }

    // 这里使用了 FastJson 进行实现，各App可以使用已经有的 JSON 库进行实现
    static public class FastJsonConverter implements JsonConverter {
        @Override
        public String toJson(Object obj) throws Exception {
            //return JSON.toJSONString(obj);
            return new Gson().toJson(obj);
        }
    }


}
