package com.intl.lib_common.util

import android.content.Context
import android.content.SharedPreferences

object PrefUtil {
    private fun getSp(context: Context): SharedPreferences {
        return context.getSharedPreferences("any-unique-name-123", Context.MODE_PRIVATE)
    }

    fun putString(context: Context, key: String, value: String?) {
        getSp(context).edit().putString(key, value).commit()
    }

    fun removeString(context: Context, key: String) {
        getSp(context).edit().remove(key).commit()
    }

    fun getString(context: Context, key: String): String? {
        return getSp(context).getString(key, null)
    }

    fun putInt(context: Context, key: String, value: Int) {
        getSp(context).edit().putInt(key, value).commit()
    }

    fun getInt(context: Context, key: String, def: Int): Int {
        return getSp(context).getInt(key, def)
    }

    fun putLong(context: Context, key: String, value: Long) {
        getSp(context).edit().putLong(key, value).commit()
    }

    fun getLong(context: Context, key: String, def: Long): Long {
        return getSp(context).getLong(key, def)
    }

    fun putBool(context: Context, key: String, value: Boolean) {
        getSp(context).edit().putBoolean(key, value).commit()
    }

    fun getBool(context: Context, key: String, def: Boolean): Boolean {
        return getSp(context).getBoolean(key, def)
    }

}