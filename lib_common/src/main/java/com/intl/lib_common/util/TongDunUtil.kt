package com.intl.lib_common.util

import android.util.Log
//import cn.tongdun.mobrisk.TDRisk
//import com.idnadakredit.loan.controller.BizConfig
//import com.idnadakredit.loan.interfaze.ApiService
//import com.idnadakredit.loan.model.bean.api.TongDunEventCollect
//import com.idnadakredit.loan.model.http.retrofit.RetrofitFactory
import com.ppdai.module.datacollection.utils.DataConfig.getApplicationContext
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch


/**
 * Created by taiyating on 2022/3/18.
 *
 * Description: 为同盾sdk封装的工具类
 *
 * https://aurora.tongdun.net/creditfp/document.htm
 * 登录账号：<EMAIL>   Abc@123456
 */
object TongDunUtil {
    private val PRINT_LOG = true

    fun collect(upload: (blackBox: String) -> Unit) {
//        try {
//            d("collect()")
//            d("getBlackBox()")
//            TDRisk.getBlackBox { blackBox ->
//                d("getBlackBox callback, blackBox=$blackBox")
//                upload(blackBox)
//            }
//        } catch (e: Exception) {
//            e.printStackTrace()
//        }
    }

    fun init(partnerCode:String, appName:String, appKey:String) {
//        try {
//            val builder = TDRisk.Builder()
//                .partnerCode(partnerCode) // Partner code,such as demo,please fill in your partner, get from trustDecision
//                .appName(appName) // app Name,such as appName,please fill in your app Name
//                .appKey(appKey) // configure AppKey, please contact TrustDecision Operations to obtain it
//                .country(TDRisk.COUNTRY_IDNA) // Country parameter，E.g: cn、sg、us、fra
//                //.disableGPS()                 // Collect GPS information by default, you can call this method to close
//                //.disableSensor()              // Collect sensor information by default, you can call this method to close
//                .disableReadPhone()           // By default, information that requires READ_PHONE_STATE permission is collected, and this method can be called to close it
//                .disableRunningTasks()        // The default collection running task, you can call this method to close
//                .disableInstallPackageList()  // By default, the list of installation packages is collected, and this method can be called to close
//                .language(3)                  // default:1, 1-Simplified Chinese, 2-Traditional Chinese, 3-English, 4-Japanese, 5-Korean, 6-Malay, 7-Thai, 8-Indonesian, 9-Russian
//
//            //if (It is recommended that users agree to the privacy agreement to initialize){
//            TDRisk.initWithOptions(getApplicationContext(), builder)
//        } catch (e: Exception) {
//            e.printStackTrace()
//        }
    }

    private fun d(text: String) {
        if (PRINT_LOG) {
            Log.d("mayqx", text)
        }
    }

}
