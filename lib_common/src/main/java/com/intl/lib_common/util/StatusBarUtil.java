
package com.intl.lib_common.util;

import android.app.Activity;
import android.graphics.Color;
import android.os.Build;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;


import java.lang.reflect.Field;
import java.lang.reflect.Method;

/**
 * Created by star on 2017/12/7.
 */

public final class StatusBarUtil {

    public static final int DEFAULT_COLOR = 0x80c6c6c6;

    private StatusBarUtil() {

    }

    /**
     * 设置状态栏透明
     * 效果会变成沉浸 需要自己加view或者padding下来
     * 4.4 可以设置透明状态栏
     * 5.0 可以设置状态栏颜色
     * 6.0 可以设置状态栏字颜色
     */
    public static void setTransparentStatusImmerse(Activity activity,boolean darkFont) {
        // 5.0以上系统状态栏透明
        Window window = activity.getWindow();
        window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
        window.getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                | View.SYSTEM_UI_FLAG_LAYOUT_STABLE);
        window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
        window.setStatusBarColor(Color.TRANSPARENT);
        if(darkFont){
            setDarkFontStatusImmerse(activity);
        }
    }

    /**
     * 修改状态栏黑色字体
     * 该方法在 setTransparentStatusImmerse 之后使用
     *
     * @param activity
     */
    private static void setDarkFontStatusImmerse(Activity activity) {
        //常规处理
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            activity.getWindow().getDecorView().setSystemUiVisibility(
                    View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                            | View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR);
        } else {
            Window window = activity.getWindow();
            activity.getWindow().addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
            activity.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
            window.setStatusBarColor(DEFAULT_COLOR);
        }
    }

    public static void setDarkFontStatus(Activity activity) {
        //常规处理
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            activity.getWindow().getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR);
            //activity.getWindow().getDecorView().setSystemUiVisibility(0);//白色
        } else {
            Window window = activity.getWindow();
            activity.getWindow().addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
            activity.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
            window.setStatusBarColor(DEFAULT_COLOR);
        }
    }
}
