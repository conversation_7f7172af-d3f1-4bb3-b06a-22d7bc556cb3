package com.intl.lib_common.util

import okhttp3.Call
import okhttp3.Callback
import okhttp3.Headers
import okhttp3.MediaType
import okhttp3.MultipartBody
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody
import okhttp3.Response
import java.io.File
import java.io.IOException
import java.net.URLConnection
import java.util.concurrent.TimeUnit


object NetworkUtil {
    private val JSON: MediaType = MediaType.parse("application/json")!!
    private var defaultClient: OkHttpClient = OkHttpClient.Builder()
        //.connectTimeout()
        .build()

    /**
     * 假设印尼4g网络平均上行速度为5Mbps，假设文件体积最大为10MB，则需要10*8/5=16s，我们设置60s应该够用了
     */
    private var uploadFileClient: OkHttpClient = OkHttpClient.Builder()
        .writeTimeout(60, TimeUnit.SECONDS)
        .build()

    fun post(url: String, headers: Map<String, String>, requestBody: String, callback: MyCallback?) {
        val headerBuilder = Headers.Builder().apply {
            headers.forEach {
                this.set(it.key, it.value)
            }
        }

        val request = Request.Builder()
            .url(url)
            .headers(headerBuilder.build())
            .post(RequestBody.create(JSON, requestBody))
            .build()

        defaultClient.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call?, e: IOException?) {
                callback?.onFailure(e ?: IOException("never happen?"))
            }

            override fun onResponse(call: Call?, response: Response?) {
                callback?.onResponse(response?.isSuccessful == true, response?.body()?.string() ?: "")
            }
        })
    }

    fun postFile(url: String, headers: Map<String, String>, body: Map<String, String>, formFieldName: String, file: File): Response {
        val headerBuilder = Headers.Builder().apply {
            headers.forEach {
                this.set(it.key, it.value)
            }
        }

        // 构造request body
        val bodyBuilder = MultipartBody.Builder()
            .setType(MultipartBody.FORM)
            // 添加文件
            .addFormDataPart(formFieldName, file.name, RequestBody.create(MediaType.parse(getMimeType(file)), file))

        // 添加其他参数
        body.forEach { (key, value) ->
            bodyBuilder.addFormDataPart(key, value)
        }

        val requestBody = bodyBuilder.build()

        // 构造请求
        val request = Request.Builder()
            .url(url)
            .headers(headerBuilder.build())
            .post(requestBody)
            .build()

        // 使用同步方式执行请求
        return uploadFileClient.newCall(request).execute()
    }


    private fun getMimeType(file: File): String {
        val fileName = file.name
        val mimeType = URLConnection.guessContentTypeFromName(fileName)
        return mimeType ?: "application/octet-stream" // 默认二进制流
    }


    /**
     * 自定义回调，隐藏实现细节，方便替换网络库
     */
    interface MyCallback {
        fun onResponse(httpSuccess: Boolean, responseBody: String)

        fun onFailure(e: IOException)
    }

    /**
     * 如果你不想重写所有的方法，可以继承这个类
     */
    abstract class MyCallbackImp : MyCallback {
        override fun onResponse(httpSuccess: Boolean, responseBody: String) {
            //
        }

        override fun onFailure(e: IOException) {
            //
        }
    }
}
