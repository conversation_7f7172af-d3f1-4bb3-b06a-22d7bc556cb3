package com.intl.lib_common.util;

import android.content.Context;
import android.os.Build;

import androidx.annotation.WorkerThread;

//import com.scottyab.rootbeer.RootBeer;

/**
 * 自研的设备风险采集算法
 */
public class AdaRisk {
    /**
     * 读取文件，耗时，建议放在工作线程调用
     *
     * @param context
     * @return
     */
    @WorkerThread
    public static boolean isRootSlow(Context context) {
        // 暂不支持，总是返回false
        //return new RootBeer(context).isRooted();
        return false;
    }

    /**
     * 参考 https://stackoverflow.com/questions/2799097/how-can-i-detect-when-an-android-application-is-running-in-the-emulator
     *
     * @return
     */
    public static boolean isEmulator() {
        try {
            return ((Build.MANUFACTURER.equalsIgnoreCase("Google") && Build.BRAND.equalsIgnoreCase("google") &&
                    ((Build.FINGERPRINT.startsWith("google/sdk_gphone_")
                            && Build.FINGERPRINT.endsWith(":user/release-keys")
                            && Build.PRODUCT.startsWith("sdk_gphone_")
                            && Build.MODEL.startsWith("sdk_gphone_"))
                            //alternative
                            || (Build.FINGERPRINT.startsWith("google/sdk_gphone64_")
                            && (Build.FINGERPRINT.endsWith(":userdebug/dev-keys") || Build.FINGERPRINT.endsWith(":user/release-keys"))
                            && Build.PRODUCT.startsWith("sdk_gphone64_")
                            && Build.MODEL.startsWith("sdk_gphone64_"))))
                    //
                    || Build.FINGERPRINT.startsWith("generic")
                    || Build.FINGERPRINT.startsWith("unknown")
                    || Build.MODEL.contains("google_sdk")
                    || Build.MODEL.contains("Emulator")
                    || Build.MODEL.contains("Android SDK built for x86")
                    //bluestacks
                    || "QC_Reference_Phone".equalsIgnoreCase(Build.BOARD) && !"Xiaomi".equalsIgnoreCase(Build.MANUFACTURER)
                    //bluestacks
                    || Build.MANUFACTURER.contains("Genymotion")
                    || Build.HOST.startsWith("Build")
                    //MSI App Player
                    || Build.BRAND.startsWith("generic") && Build.DEVICE.startsWith("generic")
                    || Build.PRODUCT.equalsIgnoreCase("google_sdk")
                    // another Android SDK emulator check
                    || "1".equalsIgnoreCase(System.getProperties().getProperty("ro.kernel.qemu")));
        } catch (Exception e) {
            return false;
        }
    }
}
