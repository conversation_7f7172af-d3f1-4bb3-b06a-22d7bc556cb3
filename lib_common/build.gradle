plugins {
    id "com.android.library"
    id 'org.jetbrains.kotlin.android'
}

android {
    namespace 'com.intl.lib_common'
    compileSdk rootProject.ext.compileSdkVersion

    defaultConfig {
        minSdk rootProject.ext.minSdkVersion
        targetSdk rootProject.ext.targetSdkVersion
        //versionCode rootProject.ext.appVersionCode
        //versionName rootProject.ext.appVersionName
    }

    dataBinding {
        enabled true
    }
    buildTypes {
        release {
            consumerProguardFiles 'proguard-rules.pro'
        }
        debug {
            consumerProguardFiles 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }
    lint {
        abortOnError false
    }
}

dependencies {
    api 'androidx.core:core-ktx:' + rootProject.ext.coreKtxVersion
    api 'androidx.appcompat:appcompat:' + rootProject.ext.appcompatVersion
    api 'com.google.android.material:material:1.8.0'
    api 'com.github.jenly1314:zxing-lite:3.3.0'
    api("com.xinye:xmp:1.9.28") {// 最新版本号请查看上述maven地址
        exclude group: 'com.android.support'// 如果您的App使用了support包，请添加该行代码
        //exclude group: 'com.squareup.okhttp3'// 如果您的App使用了okhttp3包，请添加该行代码
    }
    //api 'com.ppdai.module:datacollection:2.1.2'
    api project(':datacollection')
    api 'com.google.code.gson:gson:2.9.0'
    // 同盾
//    implementation('com.trustdecision.android:mobrisk:4.3.0.1') {
//        // sdk获取安装包列表
//        // exclude group: 'com.trustdecision.android', module: 'packagelist'
//        // sdk不收集READ_PHONE_STATE相关信息
//        exclude group: 'com.trustdecision.android', module: 'readphone'
//    }
    //implementation 'com.scottyab:rootbeer-lib:0.0.8' //不支持16kb对齐，先砍掉
    implementation 'com.google.android.gms:play-services-ads-identifier:18.0.1'
}
