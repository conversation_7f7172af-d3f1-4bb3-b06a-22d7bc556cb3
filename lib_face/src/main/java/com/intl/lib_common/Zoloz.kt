package com.intl.lib_common

import android.content.Context
import android.util.Log
import com.ap.zoloz.hummer.api.IZLZCallback
import com.ap.zoloz.hummer.api.ZLZConstants
import com.ap.zoloz.hummer.api.ZLZFacade
import com.ap.zoloz.hummer.api.ZLZRequest
import com.ap.zoloz.hummer.api.ZLZResponse

object Zoloz {
    fun getMeta(context: Context): String {
        return ZLZFacade.getMetaInfo(context)
    }

    fun startDetection(context: Context, rsaPub: String, zlzConfig: String, callback: MyZolozCallback?) {
        Log.d("mayqx", "唤起zoloz活体")
        val request = ZLZRequest()
        request.bizConfig = mapOf(
            ZLZConstants.CONTEXT to context,
            ZLZConstants.PUBLIC_KEY to rsaPub, //实测可选的，技术支持说要的，就放着吧
            //ZLZConstants.LOCALE to "en-US", //按这个格式
            ZLZConstants.LOCALE to "in-ID", //注意别写错
            ZLZConstants.CHAMELEON_CONFIG_PATH to "zoloz_config.zip",//从官网下载一个zip，本地解压修改打包，import到后台，下载
        )
        request.zlzConfig = zlzConfig
        Log.d("mayqx", "request.zlzConfig=${request.zlzConfig}")

        ZLZFacade.getInstance().start(request, object : IZLZCallback {
            override fun onCompleted(response: ZLZResponse) {
                Log.d("mayqx", "完成，$response")
                callback?.onComplete()
            }

            override fun onInterrupted(response: ZLZResponse) {
                Log.d("mayqx", "中断，${response.retCode}, ${response.extInfo}")
                // 没做处理，假设都是用户主动中断的
                callback?.onInterrupt()
            }
        })
    }
}
