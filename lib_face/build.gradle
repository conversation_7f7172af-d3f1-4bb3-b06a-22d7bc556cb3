plugins {
    id "com.android.library"
    id 'org.jetbrains.kotlin.android'
}

android {
    namespace 'com.intl.lib_face'
    compileSdk rootProject.ext.compileSdkVersion

    defaultConfig {
        minSdk rootProject.ext.minSdkVersion
        targetSdk rootProject.ext.targetSdkVersion
        //versionCode rootProject.ext.appVersionCode
        //versionName rootProject.ext.appVersionName
    }

    dataBinding {
        enabled true
    }
    buildTypes {
        release {
            consumerProguardFiles 'proguard-rules.pro'
        }
        debug {
            consumerProguardFiles 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }
    lint {
        abortOnError false
    }
}

dependencies {
    api 'androidx.core:core-ktx:' + rootProject.ext.coreKtxVersion
    api 'androidx.appcompat:appcompat:' + rootProject.ext.appcompatVersion

    //zoloz start
    implementation 'com.alibaba:fastjson:1.1.68.android'
    implementation "com.squareup.okio:okio:1.17.4@jar"
    implementation "com.zoloz.android.build:zolozcore:${zolozKitVersion}" //This current version number is the latest version number up to the present.
    implementation "com.zoloz.android.build:doc:${zolozKitVersion}"
    implementation "com.zoloz.android.build:face:${zolozKitVersion}"
    implementation "com.zoloz.android.build:xnn:${zolozKitVersion}"
    implementation "com.zoloz.android.build:faceguard:${zolozKitVersion}"
    implementation "com.zoloz.android.build:apsecurity:${zolozKitVersion}"
    //zoloz end
}
