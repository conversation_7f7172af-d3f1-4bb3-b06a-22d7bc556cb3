#!/bin/bash

# 打印错误信息并退出脚本
function error_exit {
  echo "Error: $1"
  exit 1
}

# 检查必要工具
function check_tools {
  command -v git >/dev/null 2>&1 || error_exit "Git is not installed."
  command -v ./gradlew >/dev/null 2>&1 || error_exit "Gradle wrapper (./gradlew) not found."
}

# 从 build.gradle.kts 中获取项目相关信息
function get_project_info {
  local BUILD_FILE="./app/build.gradle"

  # 确保文件存在
  if [[ ! -f "$BUILD_FILE" ]]; then
    error_exit "Build file $BUILD_FILE not found."
  fi

  # 提取 $BUILD_FLAVOR {...} 这样的子串
  FLAVOR_BLOCK=$(awk "/$BUILD_FLAVOR start/,/$BUILD_FLAVOR end/" "$BUILD_FILE")

  # 继续提取信息并只保留值部分
  PACKAGE_ID=$(echo "$FLAVOR_BLOCK" | awk -F '"' '/applicationId/ {print $2}')
  VERSION_NAME=$(echo "$FLAVOR_BLOCK" | awk -F '"' '/versionName/ {print $2}')
  VERSION_CODE=$(echo "$FLAVOR_BLOCK" | awk '/versionCode/ {print $2}')

  echo $PACKAGE_ID
  echo $VERSION_NAME
  echo $VERSION_CODE

  GIT_HASH=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")

  # 检查是否成功提取信息
  if [[ -z "$PACKAGE_ID" || -z "$VERSION_NAME" || -z "$VERSION_CODE" || -z "$GIT_HASH" ]]; then
    error_exit "Failed to retrieve project information from $BUILD_FILE."
  fi
}

# 打包主函数
function build_project {
  OUTPUT_FORMAT=$1 # apk 或 aab
  BUILD_FLAVOR=$2  # user 或 sales
  BUILD_TYPE=$3    # stage 或 release

  # 检查 build 类型和输出格式是否合法
  if [[ "$OUTPUT_FORMAT" != "apk" && "$OUTPUT_FORMAT" != "aab" ]]; then
    error_exit "Invalid output format. Use 'apk' or 'aab'."
  fi

  if [[ "$BUILD_TYPE" != "stage" && "$BUILD_TYPE" != "release" ]]; then
    error_exit "Invalid build type. Use 'stage' or 'release'."
  fi

   if [[ "$BUILD_FLAVOR" != "user" && "$BUILD_FLAVOR" != "sales" ]]; then
      error_exit "Invalid build flavor. Use 'user' or 'sales'."
    fi

  # 将 BUILD_TYPE 首字母大写（兼容旧版本 bash）
  BUILD_TYPE_CAPITALIZED=$(echo "$BUILD_TYPE" | tr '[:lower:]' '[:upper:]' | head -c 1)$(echo "$BUILD_TYPE" | tail -c +2)
  BUILD_FLAVOR_CAPITALIZED=$(echo "$BUILD_FLAVOR" | tr '[:lower:]' '[:upper:]' | head -c 1)$(echo "$BUILD_FLAVOR" | tail -c +2)

  # 运行 Gradle 打包命令
  if [[ "$OUTPUT_FORMAT" == "apk" ]]; then
    ./gradlew clean assemble${BUILD_FLAVOR_CAPITALIZED}${BUILD_TYPE_CAPITALIZED} || error_exit "Build failed."
  elif [[ "$OUTPUT_FORMAT" == "aab" ]]; then
    ./gradlew clean bundle${BUILD_FLAVOR_CAPITALIZED}${BUILD_TYPE_CAPITALIZED} || error_exit "Build failed."
  fi

  # 产物路径
  if [[ "$OUTPUT_FORMAT" == "apk" ]]; then
    BUILD_DIR="./app/build/outputs/apk"
  elif [[ "$OUTPUT_FORMAT" == "aab" ]]; then
    BUILD_DIR="./app/build/outputs/bundle"
  fi

  # 找到产物文件
  OUTPUT_FILE=$(find "$BUILD_DIR" -name "*.${OUTPUT_FORMAT}" | head -n 1)

  if [[ ! -f "$OUTPUT_FILE" ]]; then
    error_exit "Build output file not found."
  fi

  # 获取项目信息
  get_project_info

  # 重命名文件
  OUTPUT_NAME="${PACKAGE_ID}-${VERSION_NAME}-${VERSION_CODE}-${BUILD_TYPE}-${GIT_HASH}.${OUTPUT_FORMAT}"
  mv "$OUTPUT_FILE" "$(pwd)/${OUTPUT_NAME}" || error_exit "Failed to rename output file."

  echo "Build successful: $(pwd)/${OUTPUT_NAME}"

  # 记录完整路径
  RENAMED_FILE="$(pwd)/${OUTPUT_NAME}"
}

function upload_to_pgyer {
  if [[ ! -f "$RENAMED_FILE" ]]; then
    echo "Error: $RENAMED_FILE not found."
    exit 1
  fi

  PGYER_API_KEY="695d49c18112cb9dc908d4a96b323721"
  get_git_logs

  DESCRIPTION=$(printf "%s\n\n%s" "$BUILD_TYPE" "$GIT_LOGS")
  RESPONSE=$(curl -X POST "https://www.pgyer.com/apiv2/app/upload" \
      -F "file=@${RENAMED_FILE}" \
      -F "_api_key=${PGYER_API_KEY}" \
      -F "buildUpdateDescription=${DESCRIPTION}")


 FILE_URL=$(echo "$RESPONSE" | jq -r '.data.buildShortcutUrl')
 QR_URL=$(echo "$RESPONSE" | jq -r '.data.buildQRCodeURL')
 BUILD_NUMBER=$(echo "$RESPONSE" | jq -r '.data.buildBuildVersion')

 if [[ "$FILE_URL" == "null" || -z "$FILE_URL" ]]; then
   echo "File upload failed. No download URL found."
   exit 1
 fi

 echo "File uploaded to Pgyer."
 echo "Build number: $BUILD_NUMBER"
 echo "Download URL: https://www.pgyer.com/$FILE_URL"
 echo "QR URL: $QR_URL"
}

function upload_to_firim {
  if [[ ! -f "$RENAMED_FILE" ]]; then
    echo "Error: $RENAMED_FILE not found."
    exit 1
  fi

  get_git_logs
  DESCRIPTION=$(printf "%s\n\n%s" "$BUILD_TYPE" "$GIT_LOGS")

  # 设置 fir.im 的 API Token
  API_TOKEN="22e83d6327cb4adbd55a106e85f3d5c0"

  # 获取上传凭证
  echo "正在获取上传凭证..."
  UPLOAD_INFO=$(curl -X "POST" "http://api.appmeta.cn/apps" \
                     -H "Content-Type: application/json" \
                     -d "{\"type\":\"android\", \"bundle_id\":\"$PACKAGE_ID\", \"api_token\":\"$API_TOKEN\"}")

  echo "$UPLOAD_INFO"

  # 解析上传凭证
  UPLOAD_URL=$(echo "$UPLOAD_INFO" | jq -r .cert.binary.upload_url)
  KEY=$(echo "$UPLOAD_INFO" | jq -r .cert.binary.key)
  TOKEN=$(echo "$UPLOAD_INFO" | jq -r .cert.binary.token)
  APP_ID=$(echo "$UPLOAD_INFO" | jq -r .id)
  DOMAIN=$(echo "$UPLOAD_INFO" | jq -r .download_domain)
  SHORT=$(echo "$UPLOAD_INFO" | jq -r .short)

  if [ -z "$UPLOAD_URL" ] || [ -z "$KEY" ] || [ -z "$TOKEN" ] || [ -z "$APP_ID" ]; then
      echo "错误：获取上传凭证失败"
      echo "$UPLOAD_INFO"
      exit 1
  fi

  # 设置app name
  APP_NAME="YesssCredit.$BUILD_FLAVOR"

  # 上传文件
  echo "正在上传 $RENAMED_FILE"
  UPLOAD_RESULT=$(curl   -F "key=$KEY" \
                         -F "token=$TOKEN" \
                         -F "file=@$RENAMED_FILE" \
                         -F "x:name=$APP_NAME"             \
                         -F "x:version=$VERSION_NAME"         \
                         -F "x:build=$GIT_HASH"               \
                         -F "x:changelog=$DESCRIPTION" \
                         "$UPLOAD_URL")

  # 检查上传结果
  if echo "$UPLOAD_RESULT" | grep -q "is_completed"; then
      echo "上传成功！"
  else
      echo "上传失败："
      echo "$UPLOAD_RESULT"
      exit 1
  fi

  # 获取应用的下载短链接
  DOWNLOAD_URL="https://$DOMAIN/$SHORT"

  if [ -z "$DOWNLOAD_URL" ]; then
      echo "错误：获取下载链接失败"
      echo "$DOWNLOAD_INFO"
      exit 1
  fi

  echo "APK 下载链接：$DOWNLOAD_URL"
}

function get_git_logs {
  # 获取最近 5 条 commit 日志，包含 hash、日期（自定义格式）、作者、message
  GIT_LOGS=$(git log -n 5 --pretty=format:"%h%n%ad %aN%n%s%n" --date=format:"%Y-%m-%d %H:%M:%S")

  # 检查是否成功获取到日志
  if [[ -z "$GIT_LOGS" ]]; then
    error_exit "Failed to retrieve git logs."
  fi
}

function convert_aab_to_apk {
  APK_OUTPUT_DIR="./temp"
  APKS_FILE="$APK_OUTPUT_DIR/temp.apks"

  KEYSTORE_FILE="./tool/release.jks"
  KEYSTORE_PASSWORD="5b!#315D"
  KEY_ALIAS="key0"
  KEY_ALIAS_PASSWORD="5b!#315D"

  if [[ -z "$RENAMED_FILE" ]]; then
    echo "No aab file found."
    exit 1
  fi

  rm -rf "$APK_OUTPUT_DIR"
  mkdir -p "$APK_OUTPUT_DIR"

  bundletool build-apks \
    --mode=universal \
    --bundle="$RENAMED_FILE" \
    --output="$APKS_FILE" \
    --ks="$KEYSTORE_FILE" \
    --ks-pass=pass:"$KEYSTORE_PASSWORD" \
    --ks-key-alias="$KEY_ALIAS" \
    --key-pass=pass:"$KEY_ALIAS_PASSWORD"

  if [[ $? -ne 0 ]]; then
    echo "APK conversion failed."
    exit 1
  fi

  unzip "$APKS_FILE" -d "$APK_OUTPUT_DIR"
  echo "APK conversion completed."
}

# 脚本入口
function main {
  check_tools

  # 检查输入参数
  if [ $# -eq 0 ]; then
    OUTPUT_FORMAT=apk
    BUILD_FLAVOR=user
    BUILD_TYPE=stage
  elif [ $# -eq 3 ]; then
    OUTPUT_FORMAT=$1
    BUILD_FLAVOR=$2
    BUILD_TYPE=$3
  else
    echo "Usage: $0 <apk|aab> <user|sales> <stage|release>"
    exit 1
  fi
  echo $0 $OUTPUT_FORMAT $BUILD_FLAVOR $BUILD_TYPE

  build_project "$OUTPUT_FORMAT" "$BUILD_FLAVOR" "$BUILD_TYPE"
  if [[ "$OUTPUT_FORMAT" == "apk" ]]; then
    #upload_to_pgyer
    upload_to_firim
  elif [[ "$OUTPUT_FORMAT" == "aab" ]]; then
    echo ".aab will not be uploaded to Pgyer/Firim."
    convert_aab_to_apk
  fi
}

main "$@"
