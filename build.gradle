// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    ext {
        minSdkVersion = 21
        compileSdkVersion = 34
        targetSdkVersion = 34
        coreKtxVersion = "1.10.0"
        appcompatVersion = "1.6.1"
        //appVersionCode = 10001
        //appVersionName = "1.0.1"
        buildTime = new Date().format('YYY-MM-dd HH:mm:ss')
    }
    Properties properties = new Properties()
    properties.load(rootProject.file('local.properties').newDataInputStream())
    properties.keys().each { key -> rootProject.ext.set(key, properties.getProperty(key))
    }
}

buildscript {
    repositories {
        mavenCentral()

        // public
        maven { url 'https://maven.aliyun.com/repository/public' }
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }
        //maven { url 'https://maven.aliyun.com/repository/spring' }
        //maven { url 'https://maven.aliyun.com/repository/spring-plugin' }
        //maven { url 'https://maven.aliyun.com/repository/grails-core' }
        //maven { url 'https://maven.aliyun.com/repository/apache-snapshots' }
        maven { url "https://jitpack.io" }

        // self
        maven { url "http://maven.repo.ppdai.com/nexus/content/groups/android/"
            allowInsecureProtocol  true}
        maven { url "http://maven.repo.ppdai.com/nexus/content/repositories/android"
            allowInsecureProtocol  true}
        maven { url "http://maven.repo.ppdai.com/nexus/content/repositories/android-release"
            allowInsecureProtocol  true}
        maven { url 'http://maven.repo.ppdai.com/nexus/content/repositories/android-snapshot'
            allowInsecureProtocol  true}
        flatDir { dirs project(':lib_common').file('libs') }
        google()

        // third
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:8.2.2'
        classpath 'org.jetbrains.kotlin:kotlin-gradle-plugin:1.8.0'
        classpath 'com.google.gms:google-services:4.3.14'
        classpath 'com.google.firebase:firebase-crashlytics-gradle:2.9.5'
        classpath 'com.sensorsdata.analytics.android:android-gradle-plugin2:4.0.3' //神策
    }
}

configurations.all {
    resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
}

allprojects {
    repositories {
        google()
        mavenCentral()

        // public
        maven { url 'https://maven.aliyun.com/repository/public' }
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }
        //maven { url 'https://maven.aliyun.com/repository/spring' }
        //maven { url 'https://maven.aliyun.com/repository/spring-plugin' }
        //maven { url 'https://maven.aliyun.com/repository/grails-core' }
        //maven { url 'https://maven.aliyun.com/repository/apache-snapshots' }
        maven { url "https://jitpack.io" }

        // self
        maven { url "http://maven.repo.ppdai.com/nexus/content/groups/android/"
            allowInsecureProtocol  true}
        maven { url "http://maven.repo.ppdai.com/nexus/content/repositories/android"
            allowInsecureProtocol  true}
        maven { url "http://maven.repo.ppdai.com/nexus/content/repositories/android-release"
            allowInsecureProtocol  true}
        maven { url 'http://maven.repo.ppdai.com/nexus/content/repositories/android-snapshot'
            allowInsecureProtocol  true}
        flatDir { dirs project(':lib_common').file('libs') }

        // third
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
