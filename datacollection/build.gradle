apply plugin: 'com.android.library'

android {
    namespace 'com.ppdai.module.datacollection'
    compileSdkVersion 34
    buildToolsVersion '25.0.0'

    defaultConfig {
        minSdkVersion 21
        targetSdkVersion 34
        versionCode 23
        versionName "2.1.2"

        testInstrumentationRunner "android.support.test.runner.AndroidJUnitRunner"

    }
    testOptions {
        unitTests.returnDefaultValues = true
        unitTests.all {
            jacoco {
                includeNoLocationClasses = true
            }
        }
    }
    buildTypes {
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }

        debug {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            testCoverageEnabled = false // 开启覆盖率支持
        }
    }
    jacoco {
        version "0.7.9"
    }

}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation 'com.android.support:support-v4:23.2.0'
    implementation 'com.google.android.gms:play-services-ads-identifier:18.0.1'

    testImplementation 'junit:junit:4.12'
    testImplementation "org.mockito:mockito-core:2.+"
    testImplementation "org.robolectric:robolectric:3.3.2"
    testImplementation 'org.json:json:20160810'

    androidTestImplementation 'org.mockito:mockito-android:2.+'
    androidTestImplementation 'com.android.support.test:runner:0.5';
    androidTestImplementation 'com.android.support.test:rules:0.5';
    androidTestImplementation 'com.android.support.test.espresso:espresso-core:2.2.2'
    androidTestImplementation 'com.android.support.test.espresso:espresso-intents:2.2.2'

}