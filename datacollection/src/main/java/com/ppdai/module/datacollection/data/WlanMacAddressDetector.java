package com.ppdai.module.datacollection.data;

import com.ppdai.module.datacollection.utils.ReflectiveMethodHelper;

import java.net.NetworkInterface;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/1/31.
 */

public final class WlanMacAddressDetector {

    private WlanMacAddressDetector() {
        throw new AssertionError();
    }

    private static String getSystemProperty(String property) throws NoSuchMethodException {
        try {
            String value = ReflectiveMethodHelper
                    .of(Class.forName("android.os.SystemProperties"))
                    .parameter(String.class, property)
                    .parameter(String.class, "")
                    .invoke("get");
            return value == null || value.length() == 1 ? null : value;
        } catch (Exception e) {
            throw new NoSuchMethodException(e.getMessage());
        }
    }

    public static String detectMacAddress() {
        try {
            NetworkInterface networkInterface = NetworkInterface.getByName(tryGetWlanName());
            if (networkInterface != null) {
                byte[] macBytes = networkInterface.getHardwareAddress();
                if (macBytes != null) {
                   return formatMacAddress(macBytes);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "02:00:00:00:00:00";
    }

    private static String formatMacAddress(byte[] macBytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : macBytes) {
            result.append(String.format("%02x:", b));
        }
        if (result.length() > 0) {
            result.deleteCharAt(result.length() - 1);
        }
        return result.toString();
    }

    private static String tryGetWlanName() {
        try {
            return getSystemProperty("wifi.interface");
        } catch (NoSuchMethodException e) {
            e.printStackTrace();
            return "wlan0";
        }
    }
}
