package com.ppdai.module.datacollection.data;

import android.content.pm.ApplicationInfo;
import android.content.pm.PackageInfo;
import android.util.Log;

import com.ppdai.module.datacollection.AcType;
import com.ppdai.module.datacollection.base.BaseDataCollectionTask;
import com.ppdai.module.datacollection.domain.ApplicationInformation;
import com.ppdai.module.datacollection.utils.DataConfig;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * Created by <PERSON><PERSON><PERSON> on 2016/3/14.
 */
public class ApplicationInfoData extends BaseDataCollectionTask {

    private static final long ONE_DAY = 1000 * 60 * 60 * 24 * 1L;

    /**
     * Preferences保存的应用程序key
     */
    private static final String KEY_APPLICATION = "key_application_last_time";

    String uuid = UUID.randomUUID().toString();

    public ApplicationInfoData() {

    }

    public ApplicationInfoData(long intervalTime) {
        this.mIntervalTime = intervalTime;

    }

    @Override
    protected String dataCollection() {
        return getApplicationInfo();
    }

    @Override
    protected String getDataType() {
        return AcType.applicationInfo.type;
    }

    @Override
    protected long getIntervalTime() {

        return mIntervalTime != 0 ? mIntervalTime : ONE_DAY;
    }


    @Override
    protected String getPreferenceKey() {
        return KEY_APPLICATION;
    }

    @Override
    protected boolean isAsyncTask() {
        return true;
    }

    @Override
    protected String getUUID() {
        return uuid;
    }

    @Override
    protected String[] getRequestPermission() {
        return new String[0];
    }


    private String getApplicationInfo() {


        List<ApplicationInformation> applicationInformationList = getApplicationData();
        if (applicationInformationList == null || applicationInformationList.size() == 0) {
            return null;
        }
        try {
            return DataConfig.getJsonConvert().toJson(applicationInformationList);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public List<ApplicationInformation> getApplicationData() {

        List<ApplicationInformation> applicationInformationList = new ArrayList<>();

        List<PackageInfo> packageInfoList = DataConfig.getApplicationContext().getPackageManager().getInstalledPackages(0);

        for (PackageInfo packageInfo : packageInfoList) {

            ApplicationInfo appInfo = packageInfo.applicationInfo;
            if ((appInfo.flags & ApplicationInfo.FLAG_SYSTEM) > 0) {
                //系统程序 忽略
            } else {
                //非系统程序
                ApplicationInformation applicationInformation = new ApplicationInformation();
                applicationInformation.AppName = appInfo.loadLabel(DataConfig.getApplicationContext().getPackageManager()).toString();
                applicationInformation.AppCompany = appInfo.packageName;
                applicationInformation.AppVer = packageInfo.versionName;
                applicationInformation.AppInstallTime = packageInfo.firstInstallTime;
                applicationInformation.AppSize = "0";
                applicationInformation.AppWebsite = "";

                applicationInformationList.add(applicationInformation);
            }
        }
        if(applicationInformationList.size()>0) {
            Log.d("mayqx", "sdk采集AppList成功：size=" + applicationInformationList.size() + ", 采样数据=" + applicationInformationList.get(0).AppName);
        } else {
            Log.d("mayqx", "sdk采集AppList失败");
        }
        return applicationInformationList;
    }

}
