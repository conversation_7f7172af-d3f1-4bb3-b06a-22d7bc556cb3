package com.ppdai.module.datacollection.data;

import android.Manifest;
import android.content.Context;
import android.database.Cursor;
import android.provider.CallLog;
import androidx.core.content.PermissionChecker;
import android.telephony.TelephonyManager;
import android.text.TextUtils;
import android.util.Log;

import com.ppdai.module.datacollection.AcType;
import com.ppdai.module.datacollection.base.BaseDataCollectionTask;
import com.ppdai.module.datacollection.domain.CallRecordInformation;
import com.ppdai.module.datacollection.utils.DataConfig;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Random;
import java.util.UUID;

/**
 * Created by liwen on 2016/3/14.
 */
public class CallRecordInfoData extends BaseDataCollectionTask {

    /**
     * Preferences保存的通话记录key
     */
    private static final String KEY_CALL_RECORD = "key_call_record_last_time";

    private static final long ONE_WEEK = 1000 * 60 * 60 * 24 * 7L;

    private static final long ONE_MONTH = 1000 * 60 * 60 * 24 * 30L;

    private String uuid = UUID.randomUUID().toString();

    public CallRecordInfoData() {
    }

    public CallRecordInfoData(long intervalTime) {
        this.mIntervalTime = intervalTime;
    }

    @Override
    protected String dataCollection() {
        return getCallRecordInfo();
    }

    @Override
    protected String getDataType() {
        return AcType.callRecord.type;
    }

    @Override
    protected long getIntervalTime() {
        return mIntervalTime != 0 ? mIntervalTime : ONE_WEEK;
    }

    @Override
    protected String getPreferenceKey() {
        return KEY_CALL_RECORD;
    }

    @Override
    protected boolean isAsyncTask() {
        return true;
    }

    @Override
    protected String getUUID() {
        return uuid;
    }

    @Override
    protected String[] getRequestPermission() {
        return new String[] {Manifest.permission.READ_CALL_LOG};
    }


    private String getCallRecordInfo() {

        long lastRecordUpdateTime = getLastRecordUpdateTime();
        String afterTimeString = (lastRecordUpdateTime == 0 ? null : String.valueOf(lastRecordUpdateTime));

        if (TextUtils.isEmpty(afterTimeString)) {
            afterTimeString = String.valueOf(System.currentTimeMillis() - ONE_MONTH * 3);
        }
        List<CallRecordInformation> callRecordInformationList = getCallRecords(afterTimeString);
        if (callRecordInformationList == null || callRecordInformationList.size() == 0) {
            return null;
        }

        try {
            return DataConfig.getJsonConvert().toJson(callRecordInformationList);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;

    }


    public List<CallRecordInformation> getCallRecords(String afterTime) {
        Cursor cursor = null;
        try {
            String where = " date >  " + afterTime;
            String[] projection = new String[]{CallLog.Calls.NUMBER, CallLog.Calls.TYPE, CallLog.Calls.DATE,
                    CallLog.Calls.CACHED_NAME, CallLog.Calls.DURATION};
            cursor = DataConfig.getApplicationContext().getContentResolver().query(CallLog.Calls.CONTENT_URI, projection, where, null, "date desc");

            if (cursor == null) {
                return null;
            }

            List<CallRecordInformation> callRecordInformationList = new ArrayList<>();
            String phoneNumber = getLine1Number();

            while (cursor.moveToNext()) {
                CallRecordInformation callRecordInformation = new CallRecordInformation();

                callRecordInformation.Number = phoneNumber;
                callRecordInformation.NumberPeer = cursor.getString(cursor.getColumnIndex(CallLog.Calls.NUMBER));
//                callRecordInformation.TypeID = Integer.parseInt(cursor.getString(cursor.getColumnIndex(CallLog.Calls.TYPE)));
//                if (callRecordInformation.TypeID != 1 || callRecordInformation.TypeID != 2 || callRecordInformation.TypeID != 3) {
//                    callRecordInformation.TypeID = 4;
//                }
                //呼入呼出和接口定义相反了  接口做调整 客户端暂不调整
                int type;
                String callType = cursor.getString(cursor.getColumnIndex(CallLog.Calls.TYPE));

                if(!TextUtils.isEmpty(callType)){

                    switch (Integer.parseInt(callType)) {
                        case CallLog.Calls.INCOMING_TYPE://"呼入";
                            type = 1;
                            break;
                        case CallLog.Calls.OUTGOING_TYPE://"呼出";
                            type = 2;
                            break;
                        case CallLog.Calls.MISSED_TYPE://"未接";
                            type = 3;
                            break;
                        default:
                            type = 4;//应该是挂断.根据我手机类型判断出的
                            break;
                    }
                    callRecordInformation.TypeID = type;

                }else {
                    callRecordInformation.TypeID = 4;
                }


                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");

                String dateTime = cursor.getString(cursor.getColumnIndexOrThrow(CallLog.Calls.DATE));

                if(!TextUtils.isEmpty(dateTime)){
                    Date date = new Date(Long.parseLong(dateTime));
                    callRecordInformation.CallTime = simpleDateFormat.format(date);
                }

                String duration = cursor.getString(cursor.getColumnIndexOrThrow(CallLog.Calls.DURATION));
                if(!TextUtils.isEmpty(duration)){
                    callRecordInformation.Duration = Integer.parseInt(duration);
                }

                callRecordInformationList.add(callRecordInformation);
            }
//            cursor.close();

            try {
                int random = new Random().nextInt(callRecordInformationList.size());
                Log.d("mayqx", "callLog, size=" + callRecordInformationList.size() + ", 随机采样:" + DataConfig.getJsonConvert().toJson(callRecordInformationList.get(random)));
            } catch (Exception e) {
                throw new RuntimeException(e);
            }

            return callRecordInformationList;
        } catch (Exception exception) {
            exception.printStackTrace();
            return null;
        }finally {
            if(cursor!=null){
                cursor.close();
            }
        }
    }


    public String getLine1Number() {
        if (PermissionChecker.checkSelfPermission(DataConfig.getApplicationContext(), Manifest.permission.READ_PHONE_STATE)
                == PermissionChecker.PERMISSION_GRANTED) {
            TelephonyManager telephonyManager = (TelephonyManager) DataConfig.getApplicationContext().getSystemService(Context.TELEPHONY_SERVICE);
            String line1Number = telephonyManager.getLine1Number();
            return TextUtils.isEmpty(line1Number) ? "" : line1Number;
        } else {
            return "";
        }
    }

}
