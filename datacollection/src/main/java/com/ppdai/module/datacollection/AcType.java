package com.ppdai.module.datacollection;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/7/4.
 */
public enum AcType {
    /** 手机设备信息 */
    mobileInfo("1"),
    /** 手机联系人信息类型 */
    contacts("2"),
    /** 手机位置信息 */
    locationInfo("3"),
    /** 通话记录 */
    callRecord("4"),
    /** 手机短信信息 */
    messageRecord("5"),
    /** 手机应用信息 */
    applicationInfo("6"),
    /** 图片信息 */
    imageAttrInfo("7"),
    /** 网络信息（当前连接的） **/
    network("8"),
    /** wifi列表 **/
    wifiList("9"),
    /** app使用情况 **/
    appUsage("10")
    ;

    public String type;

    AcType(String type) {
        this.type = type;
    }
}
