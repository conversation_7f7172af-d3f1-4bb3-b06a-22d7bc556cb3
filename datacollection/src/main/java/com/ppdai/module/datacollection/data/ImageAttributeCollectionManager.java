package com.ppdai.module.datacollection.data;

import android.Manifest;
import android.content.Context;
import android.os.AsyncTask;
import androidx.core.content.PermissionChecker;
import android.util.Log;

import com.ppdai.module.datacollection.AcType;
import com.ppdai.module.datacollection.BuildConfig;
import com.ppdai.module.datacollection.base.BaseDataCollectionTask;
import com.ppdai.module.datacollection.base.ResultCallback;
import com.ppdai.module.datacollection.data.ImageAttributeTask.ResultHolder;
import com.ppdai.module.datacollection.utils.DataConfig;
import com.ppdai.module.datacollection.utils.DataEncoder;
import com.ppdai.module.datacollection.utils.PreferencesUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

import static java.lang.System.currentTimeMillis;

/**
 * 图片数据采集管理器
 * <p>
 * 图片数据采集器，每次只上传 {@link DataConfig#getImageAttrUploadOnceCountLimit()} 张，上传成功后，不进行下次上传任务，
 * 如果发生异常，则上传中断，等到下一次触发数据采集行为后，再断点续传数据。
 * <p>
 * 断点续传的逻辑是通过数据库的 id 实现的。每次上传成功后，记录下数据的Top、Bottom 两个id,
 * 下次查询的时候，只取 大于TopId 和 小于 BottomId 的数据，类似中间向两边蔓延的效果。
 * <p>
 * <pre>
 *                         UPLOADED DATA
 *          NEW DATA      +<----------->+     OLD DATA
 *                        |             |
 *        ---------+------+------+------+-------------+-------  DATABASE COLUMN
 *          |      |      |      |      |      |      |
 *          |      |      |      |      |      |      |
 *                  <-----+             +----->
 *                     TopId            BottomId
 * </pre>
 */
public final class ImageAttributeCollectionManager {

    private static final long ONE_DAY = 24 * 60 * 60 * 1000L;
    private static final String KEY_PREF = "key_img_attr_last_time";
    private static final String KEY_PREF_IMG_TOP_ID = "key_img_attr_top_id";
    private static final String KEY_PREF_IMG_BOTTOM_ID = "key_img_attr_bottom_id";

    private static final class HOLD {
        static ImageAttributeCollectionManager instance =
                new ImageAttributeCollectionManager(DataConfig.getApplicationContext());
    }

    public static ImageAttributeCollectionManager get() {
        return HOLD.instance;
    }

    private Context mContext;
    private int mLimit = 1000;

    protected ImageAttributeCollectionManager(Context context) {
        mContext = context.getApplicationContext();
    }

    public void start(int limit) {
        if (PermissionChecker.PERMISSION_GRANTED
                != PermissionChecker.checkSelfPermission(mContext,
                Manifest.permission.READ_EXTERNAL_STORAGE)) {
            Log.d("##", "Permission denied.");
            return;
        }

        if (isInCoolDownPeriod()) {
            Log.d("##", "In cool down period.");
            return;
        }

        if (limit < 1) {
            Log.d("##", "Limit[" + limit + "] < 1.");
            return;
        }

        mLimit = limit;
        doNext();
    }

    private boolean isInCoolDownPeriod() {
        long interval = System.currentTimeMillis() - getLastUploadTime();
        if (BuildConfig.DEBUG) {
            return interval <= TimeUnit.MILLISECONDS.convert(2, TimeUnit.MINUTES);
        } else {
            return interval <= ONE_DAY;
        }
    }

    private long getLastUploadTime() {
        return PreferencesUtils.getLong(mContext, KEY_PREF + DataConfig.getUserId(), 0);
    }

    private void saveCurrentUploadTime() {
        PreferencesUtils.putLong(mContext, KEY_PREF + DataConfig.getUserId(), currentTimeMillis());
    }

    private void saveTopAndBottomId(long topId, long bottomId) {
        PreferencesUtils.putLong(mContext, KEY_PREF_IMG_TOP_ID + DataConfig.getUserId(), topId);
        PreferencesUtils.putLong(mContext, KEY_PREF_IMG_BOTTOM_ID + DataConfig.getUserId(), bottomId);
    }

    private void doNext() {
        long topId = PreferencesUtils.getLong(mContext, KEY_PREF_IMG_TOP_ID + DataConfig.getUserId(), -1);
        long bottomId = PreferencesUtils.getLong(mContext, KEY_PREF_IMG_BOTTOM_ID + DataConfig.getUserId(), -1);

        doNext(topId, bottomId);
    }

    private void doNext(final long topId, final long bottomId) {
        Log.d("##", "doNext [" + topId + ", " + bottomId + "]");

        new AsyncImageAttrCollectAndUploadTask(topId, bottomId, mLimit,
                new AsyncImageAttrCollectAndUploadTask.ImageAttrUploadResultCallback() {
                    @Override
                    public void success(long topId, long bottomId) {
                        saveCurrentUploadTime();
                        saveTopAndBottomId(topId, bottomId);
                        // 不需要上传下一次数据，一次传完就结束了。
//                        doNext(topId, bottomId);
                    }
                }).execute();
    }

    private static class AsyncImageAttrCollectAndUploadTask extends AsyncTask<Void, Void, ResultHolder<Boolean>> {

        interface ImageAttrUploadResultCallback {
            void success(long topId, long bottomId);
        }

        private long topId;
        private long bottomId;
        private int limit;
        private ImageAttrUploadResultCallback imageAttrUploadResultCallback;

        public AsyncImageAttrCollectAndUploadTask(long topId, long bottomId, int limit, ImageAttrUploadResultCallback callback) {
            this.topId = topId;
            this.bottomId = bottomId;
            this.limit = limit;
            imageAttrUploadResultCallback = callback;
        }

        @Override
        protected void onPostExecute(ResultHolder<Boolean> result) {
            super.onPostExecute(result);
            if (!result.isEmpty()
                    && result.data) {
                // upload succeed.
                if (imageAttrUploadResultCallback != null) {
                    imageAttrUploadResultCallback.success(
                            Math.max(topId, result.topId),
                            // bottomId 有可能是 -1 的情况
                            bottomId == -1 ? result.bottomId : Math.min(bottomId, result.bottomId));
                }
            } else {
                Log.d("##", "AsyncImageAttrCollectAndUploadTask failed.");
            }
            imageAttrUploadResultCallback = null;
        }

        @Override
        protected void onCancelled() {
            super.onCancelled();
            imageAttrUploadResultCallback = null;
        }

        @Override
        protected ResultHolder<Boolean> doInBackground(Void... voids) {
            ResultHolder<DataEncoder.CryptoResult> resultHolder = new ImageAttributeTask(topId, bottomId, limit).call();
            if (resultHolder.isEmpty()) {
                return ResultHolder.empty();
            }

            // transform async request to sync.
            CountDownLatch syncLatch = new CountDownLatch(1);
            Map<String, String> params = buildRequestParams(resultHolder.data.data, resultHolder.data.aesKey);
            SyncResultCallback syncCallback = new SyncResultCallback(syncLatch);
            DataConfig.getUploadHandler().uploadData(params, DataConfig.API_DATA_COLLECTION_INFO, syncCallback);

            try {
                // wait for the response.
                syncLatch.await(3, TimeUnit.MINUTES);
                // check if something wrong happened.
                syncCallback.check();
            } catch (Exception e) {
                Log.e("##", "Upload image attribute fail.", e);
                return ResultHolder.empty();
            }
            return ResultHolder.success(resultHolder.topId, resultHolder.bottomId, true);
        }

        private Map<String, String> buildRequestParams(String data, String aesKey) {
            Map<String, String> params = new HashMap<>();
            params.put("SourceType", DataConfig.getBusinessType());
            params.put("AcType", AcType.imageAttrInfo.type);
            params.put("appdata", data);
            params.put("AppVer", DataConfig.getAppVersionName());
            params.put("OS", "2");
            params.put("secretKey", aesKey);
            return params;
        }
    }

    private static class SyncResultCallback implements ResultCallback {

        private CountDownLatch mCountDownLatch;

        private Throwable mThrowable;

        public SyncResultCallback(CountDownLatch countDownLatch) {
            mCountDownLatch = countDownLatch;
        }

        @Override
        public void uploadSuccess(String response) {
            try {
                if (BaseDataCollectionTask.isUploadSuccess(response)) {
                    mThrowable = null;
                } else {
                    throw new Exception("Response failed.");
                }
            } catch (Exception e) {
                mThrowable = e;
            }
            mCountDownLatch.countDown();
        }

        @Override
        public void uploadFail(Throwable t) {
            mThrowable = t;
            mCountDownLatch.countDown();
        }

        public void check() throws Exception {
            if (mThrowable != null) {
                throw new Exception(mThrowable);
            }
        }
    }
}
