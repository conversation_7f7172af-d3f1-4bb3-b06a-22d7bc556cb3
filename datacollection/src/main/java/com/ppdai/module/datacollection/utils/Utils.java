package com.ppdai.module.datacollection.utils;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/5/9.
 */
public final class Utils {
    private Utils() {
        throw new AssertionError();
    }

    private static final int INT_IP_ADDRESS_SIZE = 4;
    private static final int BYTE_MASK = 0xFF;
    private static final int BYTE_BIT_24 = 24;
    private static final int BYTE_BIT_16 = 16;
    private static final int BYTE_BIT_8 = 8;
    private static final int ADDR_INDEX_0 = 0;
    private static final int ADDR_INDEX_1 = 1;
    private static final int ADDR_INDEX_2 = 2;
    private static final int ADDR_INDEX_3 = 3;

    public static String formatIntIpAddress(int intIpAddress) {
        byte[] addr = new byte[INT_IP_ADDRESS_SIZE];

        addr[ADDR_INDEX_0] = (byte) (intIpAddress & BYTE_MASK);
        addr[ADDR_INDEX_1] = (byte) ((intIpAddress >>> BYTE_BIT_8) & BYTE_MASK);
        addr[ADDR_INDEX_2] = (byte) ((intIpAddress >>> BYTE_BIT_16) & BYTE_MASK);
        addr[ADDR_INDEX_3] = (byte) ((intIpAddress >>> BYTE_BIT_24) & BYTE_MASK);
        return (addr[ADDR_INDEX_0] & BYTE_MASK)
                + "." + (addr[ADDR_INDEX_1] & BYTE_MASK)
                + "." + (addr[ADDR_INDEX_2] & BYTE_MASK)
                + "." + (addr[ADDR_INDEX_3] & BYTE_MASK);
    }

}
