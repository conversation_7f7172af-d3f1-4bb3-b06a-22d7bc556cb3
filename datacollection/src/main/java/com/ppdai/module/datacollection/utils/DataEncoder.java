package com.ppdai.module.datacollection.utils;

import android.util.Base64;

import com.ppdai.module.datacollection.utils.crypto.AESUtils;
import com.ppdai.module.datacollection.utils.crypto.GZipUtils;
import com.ppdai.module.datacollection.utils.crypto.RSAUtils;

import java.util.Random;

/**
 * 数据采集 数据加密器。调用 {@link #encoder(String)} 方法可以得到 {@link CryptoResult}，是对密文和密钥的封装。
 *
 * <ol> 以下是 密文和密钥的生成算法。
 * <li> 将原始数据用 GZip 进行压缩，结果标记为 GZIP_DATA.
 * <li> 随机生成 16 个字节的 byte 数组，将其用作 AES 的 key，标记为 AES_KEY。
 * <li> 利用 AES_KEY 和默认的 AES_IV 对 GZIP_DATA 进行 AES 加密，标记为 AES_GZIP_DATA。
 * <li> 将 AES_GZIP_DATA 进行 Base64 转码，记为 BASE64_AES_GZIP_DATA。
 * <li> 为了保证 AES_KEY 的安全性，将 AES_KEY 用 RSA 的公钥进行加密，再用 Base64 进行转码，结果标记为 BASE64_RSA_AES_KEY。
 * </ol>
 */
public class DataEncoder {

    private static final String RSA_PUB_KEY = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCB" +
            "iQKBgQDUSfmpIDqQTIkqg+7AEgiFeRZGYBnJI/na3Jw/syeLG2r1XVr0sxdfT8PX8z" +
            "Y+yMQJ2QSnSPJQTd6NgnoY4z0RR20vi7ks7N+IJWOVtAAMP7kYFzE3yKym+cCKWm6U" +
            "atLGfgnx9jv3P4cZrAOh9ZKHQoQDCAMO9pJdZE2QmBwFgQIDAQAB";
    private static final byte[] AES_IV = new byte[]{0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0};

    private static final ThreadLocal<DataEncoder> ENCODER = new ThreadLocal<DataEncoder>() {
        @Override
        protected DataEncoder initialValue() {
            return new DataEncoder();
        }
    };

    public static DataEncoder get() {
        return ENCODER.get();
    }

    private Random mRandom;

    private DataEncoder() {
        mRandom = new Random();
    }

    public CryptoResult encoder(String data) throws Exception {
        byte[] aesKey = createAesKey();
        return CryptoResult.create(doBase64(doRsa(aesKey)), doBase64(doAesEncoder(aesKey, doGZip(data.getBytes()))));
    }

    private byte[] doRsa(byte[] data) throws Exception {
        return RSAUtils.encryptByPublicKey(data, RSA_PUB_KEY);
    }

    private byte[] doAesEncoder(byte[] aesKey, byte[] data) throws Exception {
        return AESUtils.encrypt(data, aesKey, AES_IV);
    }

    private byte[] doGZip(byte[] data) {
        return GZipUtils.compress(data);
    }

    private String doBase64(byte[] data) {
        return Base64.encodeToString(data, Base64.NO_WRAP);
    }

    private byte[] createAesKey() {
        byte[] bytes = new byte[16];
        mRandom.nextBytes(bytes);
        return bytes;
    }

    public static class CryptoResult {
        public final String aesKey;
        public final String data;

        private CryptoResult(String aesKey, String data) {
            this.aesKey = aesKey;
            this.data = data;
        }

        public static CryptoResult create(String aesKey, String data) {
            return new CryptoResult(aesKey, data);
        }
    }
}
