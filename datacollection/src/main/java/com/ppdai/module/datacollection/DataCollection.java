package com.ppdai.module.datacollection;


import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.ppdai.module.datacollection.base.BaseDataCollectionTask;
import com.ppdai.module.datacollection.base.CollectFinishCallback;
import com.ppdai.module.datacollection.base.ResultCallback;
import com.ppdai.module.datacollection.data.AppUsageData;
import com.ppdai.module.datacollection.data.ApplicationInfoData;
import com.ppdai.module.datacollection.data.CallRecordInfoData;
import com.ppdai.module.datacollection.data.ContactInfoData;
import com.ppdai.module.datacollection.data.ImageAttributeCollectionManager;
import com.ppdai.module.datacollection.data.LocationInfoData;
import com.ppdai.module.datacollection.data.MessageInfoData;
import com.ppdai.module.datacollection.data.MobileInfoData;
import com.ppdai.module.datacollection.data.WifiInfoData;
import com.ppdai.module.datacollection.data.WifiListData;
import com.ppdai.module.datacollection.utils.DataConfig;
import com.ppdai.module.datacollection.utils.DataEncoder;

import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Hashtable;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Created by liwen on 2016/3/11.
 */
public class DataCollection {

    private static final HashMap<String, String> KEY_MAPPING = new HashMap<String, String>(6);

    static {
        KEY_MAPPING.put(AcType.mobileInfo.type, "MobileInfoStatus");
        KEY_MAPPING.put(AcType.applicationInfo.type, "AppsStatus");
        KEY_MAPPING.put(AcType.locationInfo.type, "LocationStatus");
        KEY_MAPPING.put(AcType.contacts.type, "ContactsStatus");
        KEY_MAPPING.put(AcType.messageRecord.type, "MessagesStatus");
        KEY_MAPPING.put(AcType.callRecord.type, "callhistoryStatus");
    }

    private Hashtable<String, Integer> mCollectStatusRecord;
    private DebounceDelayController mDebounceDelayController;

    private DataCollection() {
        mCollectStatusRecord = new Hashtable<>(KEY_MAPPING.size());
        mDebounceDelayController = new DebounceDelayController();
    }

    private static class InstanceHolder {
        static DataCollection instance = new DataCollection();
    }

    public static DataCollection getInstance() {
        return InstanceHolder.instance;
    }

    /**
     * 注册、登录时调用
     */
    public void collectionRegisterAndLoginData() {
        preformAll(true);
    }

    /**
     * app启动时调用
     */
    public void collectionLaunch() {
        preformAll(false);
    }

    private void preformAll(boolean isIgnoreIntervalTime) {
        if (mDebounceDelayController.isShouldDelay()) {
            Log.d("##", "Debounce control.");
            return;
        }

        mDebounceDelayController.checkIn();
        perform(AcType.contacts, isIgnoreIntervalTime);
        perform(AcType.callRecord, isIgnoreIntervalTime);
        perform(AcType.messageRecord, isIgnoreIntervalTime);
        perform(AcType.applicationInfo, isIgnoreIntervalTime);
        perform(AcType.mobileInfo, isIgnoreIntervalTime);
        perform(AcType.locationInfo, isIgnoreIntervalTime);
        perform(AcType.imageAttrInfo, isIgnoreIntervalTime);
    }

    /**
     * @param type                 类型
     * @param isIgnoreIntervalTime true 忽略采集间隔时间
     */
    public void perform(AcType type, boolean isIgnoreIntervalTime) {
        if (AcType.imageAttrInfo.equals(type)) {
            ImageAttributeCollectionManager.get().start(DataConfig.getImageAttrUploadOnceCountLimit());
            return;
        }
        mappingTask(type).uploadDataCollection(isIgnoreIntervalTime);
    }

    BaseDataCollectionTask mappingTask(AcType type) {
        switch (type) {
            case contacts:
                return new ContactInfoData();
            case callRecord:
                return new CallRecordInfoData();
            case messageRecord:
                return new MessageInfoData();
            case applicationInfo:
                return new ApplicationInfoData();
            case mobileInfo:
                return new MobileInfoData();
            case locationInfo:
                return new LocationInfoData();
            case network:
                return new WifiInfoData();
            case wifiList:
                return new WifiListData();
            case appUsage:
                return new AppUsageData();
            default:
                throw new AssertionError();
        }
    }

    private void uploadGrade() {
        if (!DataConfig.getGrade()) {
            onCollectFinished();
            return;
        }
        if (!isSomeInfoCollected()) {
            onCollectFinished();
            return;
        }

        String data = null;
        try {
            data = DataConfig.getJsonConvert().toJson(mCollectStatusRecord);
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (data == null) {
            return;
        }

        String aesKey = null;
        try {
            DataEncoder.CryptoResult result = DataEncoder.get().encoder(data);
            data = result.data;
            aesKey = result.aesKey;
        } catch (Exception e) {
            Log.e("##", "encoder fail", e);
            try {
                data = URLEncoder.encode(data, "utf-8");
            } catch (Exception e1) {
                Log.e("##", "url encoder fail", e1);
            }
        }

        HashMap<String, String> params = new HashMap<>(5);
        params.put("SourceType", DataConfig.getBusinessType());
        params.put("AcType", DataConfig.TYPE_DATA_STATUS);
        params.put("appdata", data);
        params.put("AppVer", getAppVersionName());
        params.put("secretKey", aesKey);

        DataConfig.getUploadHandler().uploadData(params, DataConfig.API_DATA_COLLECTION_INFO, new ResultCallback() {
            @Override
            public void uploadSuccess(String response) {
                onCollectFinished();
            }

            @Override
            public void uploadFail(Throwable t) {
                onCollectFinished();
            }
        });
    }

    private void onCollectFinished() {
        mDebounceDelayController.resetControl();
        CollectFinishCallback collectFinishCallback = DataConfig.getCollectFinishCallback();
        if (collectFinishCallback != null) {
            collectFinishCallback.onFinish();
        }
    }

    public String getAppVersionName() {
        return DataConfig.getAppVersionName();
    }

    /**
     * 记录手机数据采集状态发生的变化
     *
     * @param type      采集的数据的类型
     * @param isSuccess true 采集成功 false 采集失败
     */
    public void recordCollectStatusChanged(String type, boolean isSuccess) {
        final String key = KEY_MAPPING.get(type);
        if (key != null) {
            mCollectStatusRecord.put(key, isSuccess ? 1 : 0);
            Log.d("###", type + "  " + isSuccess + "  " + mCollectStatusRecord.size());
            if (mCollectStatusRecord.size() == KEY_MAPPING.size()) {
                uploadGrade();
                mCollectStatusRecord.clear();
            }
        }
    }

    private boolean isSomeInfoCollected() {
        // 就算只有一个任务上传成功了，就应该打分
        return mCollectStatusRecord.values().contains(1);
    }

    /**
     * Handler 防重放控制器
     */
    private static class DebounceDelayController {
        private static final int DEFAULT_DEBOUNCE_DELAY_TIME = 6 * 60 * 1000;

        private AtomicBoolean delayControl;
        private Handler handler;

        public DebounceDelayController() {
            delayControl = new AtomicBoolean(false);
            handler = new Handler(Looper.getMainLooper());
        }

        public boolean isShouldDelay() {
            return delayControl.get();
        }

        public void checkIn() {
            if (delayControl.compareAndSet(false, true)) {
                handler.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        resetControl();
                    }
                }, DEFAULT_DEBOUNCE_DELAY_TIME);
            }
        }

        void resetControl() {
            handler.removeCallbacks(null);
            delayControl.set(false);
        }
    }
}
