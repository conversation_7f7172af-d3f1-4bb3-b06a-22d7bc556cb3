package com.ppdai.module.datacollection.utils;

import android.content.Context;

/**
 * Created by <PERSON><PERSON><PERSON> on 2016/3/11.
 */
public class RecordUpdateTimeUtils {

    /**
     * 设置上传成功的时间戳
     *
     * @param context
     * @param key
     * @param value
     */
    public static void setLastRecordUpdateTime(Context context, String key, String userID, long value) {

        PreferencesUtils.putLong(context, key + userID, value);
    }

    /**
     * 获取上传的时间戳
     *
     * @param context
     * @param key
     * @param defValue
     * @return
     */
    public static long getLastRecordUpdateTime(Context context, String key, String userID, long defValue) {

        return PreferencesUtils.getLong(context, key + userID, defValue);
    }

    /**
     * 是否需要手机信息
     *
     * @param lastRecordTime 上一次保存的时间
     * @param intevalTime    需要上传的时间间隔
     * @return
     */
    public static boolean isNeedCollectionData(long lastRecordTime, long intevalTime) {

        if ((System.currentTimeMillis() - lastRecordTime) > intevalTime) {
            return true;
        }
        return false;

    }
}
