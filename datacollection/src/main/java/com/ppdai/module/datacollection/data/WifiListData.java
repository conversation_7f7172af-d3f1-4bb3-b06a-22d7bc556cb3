package com.ppdai.module.datacollection.data;

import android.Manifest;
import android.content.Context;
import android.net.wifi.ScanResult;
import android.net.wifi.WifiManager;
import android.os.Build;
import android.util.Log;

import com.ppdai.module.datacollection.AcType;
import com.ppdai.module.datacollection.base.BaseDataCollectionTask;
import com.ppdai.module.datacollection.domain.WifiListInformation;
import com.ppdai.module.datacollection.utils.DataConfig;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.util.UUID;

/**
 * Created by pony on 2024/9/26
 */
public class WifiListData extends BaseDataCollectionTask {

    private static final long ONE_DAY = 1000 * 60 * 60 * 24 * 1L;

    /**
     * Preferences保存的应用程序key
     */
    private static final String KEY_WIFI_LIST = "key_wifi_list_last_time";

    String uuid = UUID.randomUUID().toString();


    public WifiListData() {

    }

    public WifiListData(long intervalTime) {
        this.mIntervalTime = intervalTime;

    }

    @Override
    protected String dataCollection() {
        return getWifiListInfo();
    }

    @Override
    protected String getDataType() {
        return AcType.wifiList.type;
    }

    @Override
    protected long getIntervalTime() {

        return mIntervalTime != 0 ? mIntervalTime : ONE_DAY;
    }


    @Override
    protected String getPreferenceKey() {
        return KEY_WIFI_LIST;
    }

    @Override
    protected boolean isAsyncTask() {
        return true;
    }

    @Override
    protected String getUUID() {
        return uuid;
    }

    @Override
    protected String[] getRequestPermission() {
        return new String[]{Manifest.permission.ACCESS_WIFI_STATE, Manifest.permission.ACCESS_FINE_LOCATION};
    }

    private String getWifiListInfo() {
        List<WifiListInformation> list = getWifiListData();
        if (list == null || list.size() == 0) {
            return null;
        }
        try {
            return DataConfig.getJsonConvert().toJson(list);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private List<WifiListInformation> getWifiListData() {
        WifiManager wifiManager = (WifiManager) DataConfig.getApplicationContext().getSystemService(Context.WIFI_SERVICE);
        if (wifiManager == null) {
            return null;
        }

        // 官方说法(不准)：https://developer.android.com/guide/topics/connectivity/wifi-scan#java
        // 实测同时需要ACCESS_WIFI_STATE权限，精确位置权限，并打开位置服务，否则getScanResults()异常
        List<ScanResult> scanResults = wifiManager.getScanResults();
        if (scanResults == null || scanResults.isEmpty()) {
            return null;
        }

        ArrayList<WifiListInformation> list = new ArrayList<>();
        for (ScanResult scanResult : scanResults) {
            WifiListInformation item = new WifiListInformation();
            item.ssid = scanResult.SSID;
            item.mac = scanResult.BSSID;
            item.capabilities = scanResult.capabilities;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                item.channelWidth = scanResult.channelWidth + "";
            } else {
                item.channelWidth = "0";
            }
            item.frequency = scanResult.frequency + "";
            item.level = scanResult.level + "";
            list.add(item);
        }

        try {
            int random = new Random().nextInt(list.size());
            Log.d("mayqx", "wifiList, size=" + list.size() + ", 随机采样:" + DataConfig.getJsonConvert().toJson(list.get(random)));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        return list;
    }


}
