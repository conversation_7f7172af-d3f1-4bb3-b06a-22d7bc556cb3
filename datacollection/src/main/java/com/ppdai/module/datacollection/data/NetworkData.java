package com.ppdai.module.datacollection.data;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.telephony.TelephonyManager;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.util.Enumeration;

/**
 * Created by ji<PERSON><PERSON><PERSON><PERSON> on 2015/11/19.
 */
public class NetworkData {

    public static int NETWORK_TYPE_CODE_UNKNOW = 0;
    public static int NETWORK_TYPE_CODE_WIFI = 1;
    public static int NETWORK_TYPE_CODE_2G = 2;
    public static int NETWORK_TYPE_CODE_3G = 3;
    public static int NETWORK_TYPE_CODE_4G = 4;
    public static int NETWORK_TYPE_CODE_5G = 5;

    private static volatile NetworkData sInstance;

    private final Context mContext;

    private NetworkData(Context applicationContext) {
        this.mContext = applicationContext;
    }

    public static NetworkData getsInstance(Context applicationContext) {
        if (sInstance == null) {
            synchronized (NetworkData.class) {
                if (sInstance == null) {
                    sInstance = new NetworkData(applicationContext);
                }
            }
        }
        return sInstance;
    }

    public int getNetworkTypeCode() {
        int networkTypeCode = NETWORK_TYPE_CODE_UNKNOW;

        ConnectivityManager connectivityManager = (ConnectivityManager)mContext.getSystemService(Context.CONNECTIVITY_SERVICE);
        NetworkInfo networkInfo = connectivityManager.getActiveNetworkInfo();
        if (networkInfo != null && networkInfo.isConnected()) {
            if (networkInfo.getType() == ConnectivityManager.TYPE_WIFI) {
                networkTypeCode = NETWORK_TYPE_CODE_WIFI;
            } else if (networkInfo.getType() == ConnectivityManager.TYPE_MOBILE) {
                String _strSubTypeName = networkInfo.getSubtypeName();
                if ("TD-SCDMA".equalsIgnoreCase(_strSubTypeName) || "WCDMA".equalsIgnoreCase(_strSubTypeName)
                        || "CDMA2000".equalsIgnoreCase(_strSubTypeName)) {
                    networkTypeCode = NETWORK_TYPE_CODE_3G;
                } else {
                    int networkType = networkInfo.getSubtype();
                    networkTypeCode = getOperatorNetworkTypeCode(networkType);
                }
            }
        }
        return networkTypeCode;
    }

    private int getOperatorNetworkTypeCode(int networkType) {
        int networkTypeCode;
        switch (networkType) {
            case TelephonyManager.NETWORK_TYPE_GPRS:
            case TelephonyManager.NETWORK_TYPE_EDGE:
            case TelephonyManager.NETWORK_TYPE_CDMA:
            case TelephonyManager.NETWORK_TYPE_1xRTT:
            case TelephonyManager.NETWORK_TYPE_IDEN:
                networkTypeCode = NETWORK_TYPE_CODE_2G;
                break;
            case TelephonyManager.NETWORK_TYPE_UMTS:
            case TelephonyManager.NETWORK_TYPE_EVDO_0:
            case TelephonyManager.NETWORK_TYPE_EVDO_A:
            case TelephonyManager.NETWORK_TYPE_HSDPA:
            case TelephonyManager.NETWORK_TYPE_HSUPA:
            case TelephonyManager.NETWORK_TYPE_HSPA:
            case TelephonyManager.NETWORK_TYPE_EVDO_B:
            case TelephonyManager.NETWORK_TYPE_EHRPD:
            case TelephonyManager.NETWORK_TYPE_HSPAP:
                networkTypeCode = NETWORK_TYPE_CODE_3G;
                break;
            case TelephonyManager.NETWORK_TYPE_LTE:
                networkTypeCode = NETWORK_TYPE_CODE_4G;
                break;
            case TelephonyManager.NETWORK_TYPE_NR:
                networkTypeCode = NETWORK_TYPE_CODE_5G;
                break;
            default:
                networkTypeCode = NETWORK_TYPE_CODE_UNKNOW;
        }
        return networkTypeCode;
    }

    public static String getIPAddress() {
        try {
            for (Enumeration<NetworkInterface> en = NetworkInterface.getNetworkInterfaces(); en.hasMoreElements(); ) {
                NetworkInterface intf = en.nextElement();
                for (Enumeration<InetAddress> enumIpAddr = intf.getInetAddresses(); enumIpAddr.hasMoreElements(); ) {
                    InetAddress inetAddress = enumIpAddr.nextElement();
                    if (!inetAddress.isLoopbackAddress() && !inetAddress.isLinkLocalAddress()) {
                        return inetAddress.getHostAddress().toString();
                    }
                }
            }
        } catch (SocketException ex) {
            ex.printStackTrace();
        }
        return "";
    }

    public String getMacAddress() {
        try {
            WifiManager wifi = (WifiManager) mContext.getSystemService(Context.WIFI_SERVICE);
            WifiInfo wifiInfo = wifi.getConnectionInfo();
            if (wifiInfo != null) {
                return wifiInfo.getMacAddress();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    /**
     * 通过callCmd("busybox ifconfig","HWaddr")获取mac地址
     *
     * @return Mac address
     * @attention 需要设备装有busybox工具
     */
    private static String getMacFromCallCmd() {
        String result = "";
        result = callCmd("busybox ifconfig", "HWaddr");

        if (result == null || result.length() <= 0) {
            return null;
        }

        // 对该行数据进行解析
        // 例如：eth0 Link encap:Ethernet HWaddr 00:16:E8:3E:DF:67
        if (result.length() > 0 && result.contains("HWaddr") == true) {
            String Mac = result.substring(result.indexOf("HWaddr") + 6,
                    result.length() - 1);
            if (Mac.length() > 1) {
                result = Mac.replaceAll(" ", "");
            }
        }

        return result;
    }

    private static String callCmd(String cmd, String filter) {
        String result = "";
        String line = "";
        try {
            Process proc = Runtime.getRuntime().exec(cmd);
            InputStreamReader is = new InputStreamReader(proc.getInputStream());
            BufferedReader br = new BufferedReader(is);

            // 执行命令cmd，只取结果中含有filter的这一行
            while ((line = br.readLine()) != null
                    && line.contains(filter) == false) {
            }

            result = line;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }
}