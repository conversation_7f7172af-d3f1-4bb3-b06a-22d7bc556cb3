package com.ppdai.module.datacollection.base;

import android.os.AsyncTask;
import android.os.Build;
import androidx.core.content.PermissionChecker;
import android.text.TextUtils;
import android.util.Log;

import com.ppdai.module.datacollection.DataCollection;
import com.ppdai.module.datacollection.utils.DataConfig;
import com.ppdai.module.datacollection.utils.DataEncoder;
import com.ppdai.module.datacollection.utils.DataEncoder.CryptoResult;
import com.ppdai.module.datacollection.utils.PreferencesUtils;

import org.json.JSONObject;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by liwen on 2016/3/11.
 */
public abstract class BaseDataCollectionTask {

    protected long mIntervalTime;

    /**
     * 收集数据
     */
    protected abstract String dataCollection() throws Exception;

    protected abstract String getDataType();

    protected abstract long getIntervalTime();

    protected abstract String getPreferenceKey();

    protected abstract boolean isAsyncTask();

    protected abstract String getUUID();

    protected abstract String[] getRequestPermission();

    public void uploadDataCollection(boolean isIgnoreIntervalTime) {
        boolean hasPermission = true;
        for (String permission : getRequestPermission()) {
            hasPermission = hasPermission
                    && (PermissionChecker.PERMISSION_GRANTED
                    == PermissionChecker.checkSelfPermission(DataConfig.getApplicationContext(), permission));
        }

        //如果忽略采集时间间隔 或者 需要采集数据(超过配置的采集时间) 则进行数据采集
        //权限获取失败，调用采集失败
        if (hasPermission
                && (isIgnoreIntervalTime || isNeedCollectionData())) {
            getData();
        } else {
            DataCollection.getInstance().recordCollectStatusChanged(getDataType(), false);
        }
    }

    /**
     * 处理数据
     */
    private void getData() {
        if (isAsyncTask()) { //异步
            AsyncTask<Void, Void, CryptoResult> asyncTask = new AsyncTask<Void, Void, CryptoResult>() {
                @Override
                protected CryptoResult doInBackground(Void... params) {
                    String data = null;
                    try {
                        data = dataCollection();
                    } catch (Exception e) {
                        Log.e("##", "collect fail", e);
                    }

                    if (data != null) {
                        try {
                            return DataEncoder.get().encoder(data);
                        } catch (Exception e) {
                            Log.e("##", "encoder fail", e);
                            try {
                                // 加密失败，采用降级方案
                                return CryptoResult.create(null, URLEncoder.encode(data, "utf-8"));
                            } catch (Exception e1) {
                                Log.e("##", "url encoder fail", e1);
                            }
                        }
                    }
                    return CryptoResult.create(null, data);
                }

                @Override
                protected void onPostExecute(CryptoResult data) {
                    super.onPostExecute(data);
                    //data != null 数据采集成功
                    String result = data.data;

                    if (TextUtils.isEmpty(result)) {
                        DataCollection.getInstance().recordCollectStatusChanged(getDataType(), false);
                    } else {
                        saveData(result, data.aesKey);
                    }
                }
            };
            //串行运行
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.HONEYCOMB) {
                asyncTask.executeOnExecutor(AsyncTask.SERIAL_EXECUTOR);
            } else {
                asyncTask.execute();
            }

        } else { //同步
            String data = null;
            try {
                data = dataCollection();
            } catch (Exception e) {
                Log.e("##", "collect fail", e);
            }
            if (TextUtils.isEmpty(data)) {
                DataCollection.getInstance().recordCollectStatusChanged(getDataType(), false);
                return;
            }
            try {
                CryptoResult result = DataEncoder.get().encoder(data);
                saveData(result.data, result.aesKey);
            } catch (Exception e) {
                Log.e("##", "encoder fail", e);
                try {
                    data = URLEncoder.encode(data, "utf-8");
                    saveData(data, null);
                } catch (UnsupportedEncodingException e1) {
                    Log.e("##", "url encoder fail", e1);
                }
            }
        }
    }


    /**
     * 设置保存的时间
     */
    private void setLastRecordUpdateTime() {
        Log.d(">>>", getDataType() + " & LastRecordUpdateTime = " + System.currentTimeMillis());
        PreferencesUtils.putLong(DataConfig.getApplicationContext(), getPreferenceKey() + DataConfig.getUserId(), System.currentTimeMillis());
    }

    /**
     * 获取保存的时间
     *
     * @return
     */
    protected long getLastRecordUpdateTime() {
        return PreferencesUtils.getLong(DataConfig.getApplicationContext(), getPreferenceKey() + DataConfig.getUserId(), 0);
    }


    /**
     * 是否需要收集
     *
     * @return
     */
    private boolean isNeedCollectionData() {
        if ((System.currentTimeMillis() - getLastRecordUpdateTime()) > getIntervalTime()) {
            return true;
        }
        return false;
    }


    /**
     * 保存数据
     *
     * @param data
     */
    private void saveData(String data, String secretKey) {
        Map params = new HashMap<>();
        params.put("SourceType", DataConfig.getBusinessType());
        params.put("AcType", getDataType());
        params.put("appdata", data);
        params.put("AppVer", DataConfig.getAppVersionName());
        params.put("OS", "2");
        params.put("secretKey", secretKey);


        //借入APP 设置为true 调用此方法
        if (DataConfig.getColleactionUploadTime()) {
            saveUpdateTime(DataConfig.STEP_ONE, getUUID(), getDataType());
        }

        DataConfig.getUploadHandler().uploadData(params, DataConfig.API_DATA_COLLECTION_INFO, new ResultCallback() {
            @Override
            public void uploadSuccess(String response) {
                try {
                    boolean isSuccess = isUploadSuccess(response);
                    if (isSuccess) {
                        Log.d("###", "upload success => " + getDataType());
                        DataCollection.getInstance().recordCollectStatusChanged(getDataType(), true);
                        setLastRecordUpdateTime();

                        if (DataConfig.getColleactionUploadTime()) {
                            saveUpdateTime(DataConfig.STEP_TWO, getUUID(), getDataType());
                        }
                    } else {
                        uploadFail(new Exception("code is not 0"));
                    }
                } catch (Exception e) {
                    uploadFail(e);
                    return;
                }
            }

            @Override
            public void uploadFail(Throwable t) {
                Log.e("###", "upload fail => " + getDataType(), t);
                DataCollection.getInstance().recordCollectStatusChanged(getDataType(), false);
            }
        });
    }

    public static boolean isUploadSuccess(String response) throws Exception {
        JSONObject resp = new JSONObject(response);
        int code = resp.optInt("Result", resp.optInt("ResultCode", -1));
        return code == 0;
    }

    /**
     * 保存数据上传时间
     *
     * @param step
     * @param uuid
     * @param typeId
     */
    private void saveUpdateTime(String step, String uuid, String typeId) {

        Map paramsTime = new HashMap<>();
        paramsTime.put("DataType", typeId);
        paramsTime.put("Step", step);
        paramsTime.put("RequestGuid", uuid);

        boolean isSignature = DataConfig.getIsSignature();
        if (isSignature) {
            paramsTime.put("RequestTime", new Date());
        } else {
            paramsTime.put("RequestTime", new SimpleDateFormat(DataConfig.getDateFormatPattern()).format(new Date()));
        }

        DataConfig.getUploadHandler().uploadData(paramsTime, DataConfig.API_SAVE_APP_DATA_COLLECTION_TIME, new ResultCallback() {
            @Override
            public void uploadSuccess(String response) {

            }

            @Override
            public void uploadFail(Throwable t) {

            }
        });
    }

}
