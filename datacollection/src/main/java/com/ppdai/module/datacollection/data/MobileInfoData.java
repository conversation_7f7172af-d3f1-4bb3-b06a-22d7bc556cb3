package com.ppdai.module.datacollection.data;

import android.content.Context;
import android.content.res.Resources;
import android.os.BatteryManager;
import android.os.Build;
import android.telephony.TelephonyManager;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.util.Log;

import androidx.annotation.WorkerThread;

import com.google.android.gms.ads.identifier.AdvertisingIdClient;
import com.ppdai.module.datacollection.AcType;
import com.ppdai.module.datacollection.base.BaseDataCollectionTask;
import com.ppdai.module.datacollection.domain.MobileInformation;
import com.ppdai.module.datacollection.utils.DataConfig;

import java.util.UUID;

/**
 * 设备信息收集
 * Created by liwen on 2016/3/11.
 */
public class MobileInfoData extends BaseDataCollectionTask {

    /**
     * Preferences保存的设备key
     */
    private static final String KEY_MOBILE = "key_device_last_time";

    private static final long ONE_DAY = 1000 * 60 * 60 * 24L;

    String uuid = UUID.randomUUID().toString();

    public MobileInfoData() {
    }

    public MobileInfoData(long intervalTime) {
        this.mIntervalTime = intervalTime;
    }

    @Override
    protected String dataCollection() {
        return getDeviceDataInfo();
    }

    @Override
    protected String getDataType() {
        return AcType.mobileInfo.type;
    }


    @Override
    protected long getIntervalTime() {
        return mIntervalTime != 0 ? mIntervalTime : ONE_DAY;
    }


    @Override
    protected String getPreferenceKey() {
        return KEY_MOBILE;
    }

    @Override
    protected boolean isAsyncTask() {
        return true;
    }

    @Override
    protected String getUUID() {
        return uuid;
    }

    @Override
    protected String[] getRequestPermission() {
        // 我们不申请这个权限，采集部分数据
        //return new String[]{Manifest.permission.READ_PHONE_STATE};
        return new String[0];
    }


    private String getDeviceDataInfo() {
        MobileInformation deviceInformation = new MobileInformation();
        deviceInformation.Make = getBrand();
        deviceInformation.Model = getModel();
        deviceInformation.IMSI = getIMSI();
        deviceInformation.IMEI = getIMEI();
        deviceInformation.OS = DataConfig.OS_CODE;
        deviceInformation.OSVer = getOSVerison();
        deviceInformation.Network = NetworkData.getsInstance(DataConfig.getApplicationContext()).getNetworkTypeCode();
        deviceInformation.Carrier = getSimOperatorInfo();
        deviceInformation.Number = getLine1Number();
        deviceInformation.NumberTwo = getLine2Number();
        deviceInformation.IP = NetworkData.getsInstance(DataConfig.getApplicationContext()).getIPAddress();
        deviceInformation.MAC = NetworkData.getsInstance(DataConfig.getApplicationContext()).getMacAddress();
        deviceInformation.AppVer = getAppVersionName();
        deviceInformation.AdvertisingId = getAdsId();
        deviceInformation.BatteryPercentage = getBatteryPercentage();
        deviceInformation.ScreenSize = getScreenSize();


        try {
            String data = DataConfig.getJsonConvert().toJson(deviceInformation);
            Log.d("mayqx", "device:" + data);
            return data;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;


    }

    public String getIMEI() {
        try {
            TelephonyManager telephonyManager = (TelephonyManager) DataConfig.getApplicationContext().getSystemService(Context.TELEPHONY_SERVICE);
            String IMEI = telephonyManager.getDeviceId();
            return TextUtils.isEmpty(IMEI) ? "" : IMEI;
        } catch (Exception e) {
            return "";
        }
    }

    public String getBrand() {
        return TextUtils.isEmpty(Build.BRAND) ? "" : Build.BRAND;
    }

    public String getModel() {
        return TextUtils.isEmpty(Build.MODEL) ? "" : Build.MODEL;
    }

    public String getIMSI() {
        try {
            TelephonyManager telephonyManager = (TelephonyManager) DataConfig.getApplicationContext().getSystemService(Context.TELEPHONY_SERVICE);
            String IMSI = telephonyManager.getSubscriberId();
            return TextUtils.isEmpty(IMSI) ? "" : IMSI;
        } catch (Exception e) {
            return "";
        }
    }

    public String getOSVerison() {
        String osVersion = android.os.Build.VERSION.RELEASE;
        return TextUtils.isEmpty(osVersion) ? "" : osVersion;
    }

    public String getSimOperatorInfo() {
        try {
            TelephonyManager telephonyManager = (TelephonyManager) DataConfig.getApplicationContext().getSystemService(Context.TELEPHONY_SERVICE);
            String operatorString = telephonyManager.getSimOperator();

//            if (operatorString == null) {
//                return "Unknow";
//            }
//
//            if (operatorString.equals("46000") || operatorString.equals("46002") || operatorString.equals("46007")) {
//                //中国移动
//                return "中国移动";
//            } else if (operatorString.equals("46001")) {
//                //中国联通
//                return "中国联通";
//            } else if (operatorString.equals("46003")) {
//                //中国电信
//                return "中国电信";
//            }

            return operatorString;
        } catch (Exception e) {
            return "";
        }
    }

    public String getLine1Number() {
        try {
            TelephonyManager telephonyManager = (TelephonyManager) DataConfig.getApplicationContext().getSystemService(Context.TELEPHONY_SERVICE);
            String line1Number = telephonyManager.getLine1Number();
            return TextUtils.isEmpty(line1Number) ? "" : line1Number;
        } catch (Exception e) {
            return "";
        }
    }

    public String getLine2Number() {
        return "";
    }


    public String getAppVersionName() {
        return DataConfig.getAppVersionName();
    }

    public String getBatteryPercentage() {
        try {
            BatteryManager bm = (BatteryManager) DataConfig.getApplicationContext().getSystemService(Context.BATTERY_SERVICE);
            int percent = bm.getIntProperty(BatteryManager.BATTERY_PROPERTY_CAPACITY);
            return percent + "%";
        } catch (Exception e) {
            return "";
        }
    }

    public String getScreenSize() {
        try {
            DisplayMetrics m = Resources.getSystem().getDisplayMetrics();
            return m.widthPixels + "*" + m.heightPixels;
        } catch (Exception e) {
            return "";
        }
    }


    @WorkerThread
    public String getAdsId() {
        try {
            AdvertisingIdClient.Info info = AdvertisingIdClient.getAdvertisingIdInfo(DataConfig.getApplicationContext());
            return info.getId();
        } catch (Exception e) {
            return "";
        }
    }
}
