package com.ppdai.module.datacollection.data;

import android.Manifest;
import android.annotation.SuppressLint;
import android.content.Context;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.util.Log;

import androidx.annotation.NonNull;

import com.ppdai.module.datacollection.AcType;
import com.ppdai.module.datacollection.base.BaseDataCollectionTask;
import com.ppdai.module.datacollection.domain.WifiInformation;
import com.ppdai.module.datacollection.utils.DataConfig;
import com.ppdai.module.datacollection.utils.Utils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.Random;
import java.util.UUID;

/**
 * Created by pony on 2024/9/26
 */
public class WifiInfoData extends BaseDataCollectionTask {

    private static final long ONE_DAY = 1000 * 60 * 60 * 24 * 1L;

    /**
     * Preferences保存的应用程序key
     */
    private static final String KEY_WIFI = "key_wifi_last_time";

    String uuid = UUID.randomUUID().toString();
    private SimpleDateFormat mDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS", Locale.getDefault());

    public WifiInfoData() {

    }

    public WifiInfoData(long intervalTime) {
        this.mIntervalTime = intervalTime;

    }

    @Override
    protected String dataCollection() {
        WifiInformation info = getWifiData();
        if (info == null) {
            return null;
        }
        try {
            return DataConfig.getJsonConvert().toJson(info);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    protected String getDataType() {
        return AcType.network.type;
    }

    @Override
    protected long getIntervalTime() {

        return mIntervalTime != 0 ? mIntervalTime : ONE_DAY;
    }


    @Override
    protected String getPreferenceKey() {
        return KEY_WIFI;
    }

    @Override
    protected boolean isAsyncTask() {
        return true;
    }

    @Override
    protected String getUUID() {
        return uuid;
    }

    @Override
    protected String[] getRequestPermission() {
        return new String[]{Manifest.permission.ACCESS_NETWORK_STATE};
    }

    @SuppressLint("WifiManagerPotentialLeak")
    @NonNull
    private WifiInformation getWifiData() {
        WifiInformation data = new WifiInformation();
        ConnectivityManager manager =
                (ConnectivityManager) DataConfig.getApplicationContext()
                        .getSystemService(Context.CONNECTIVITY_SERVICE);

        // 需要ACCESS_NETWORK_STATE权限
        NetworkInfo info = manager.getActiveNetworkInfo();
        if (info == null) {
            return data;
        }
        if (info != null && info.isConnected()) {
            int type = NetworkData.getsInstance(DataConfig.getApplicationContext()).getNetworkTypeCode();
            //data.AppTime = mDateFormat.format(new Date()); //有时区问题
            data.AppTime = System.currentTimeMillis() + ""; //排除时区影响
            data.NetStatus = type + "";
            data.PhoneMac = WlanMacAddressDetector.detectMacAddress();
            if (type != NetworkData.NETWORK_TYPE_CODE_WIFI) {
                // 移动网络
                data.IP = NetworkData.getIPAddress();
                //Manifest.permission.READ_PHONE_STATE
                data.NetName = "";
            }
        } else {
            // wifi网络
            try {
                // 需要ACCESS_WIFI_STATE权限，OJK可能不允许，这里try catch下，没有则为空
                WifiManager wifiManager = (WifiManager) DataConfig.getApplicationContext().getSystemService(Context.WIFI_SERVICE);
                WifiInfo wifiInfo = wifiManager.getConnectionInfo();
                if (wifiInfo == null) {
                    return data;
                }
                data.RouterMac = wifiInfo.getMacAddress();
                /**
                 * NOTE: 在8.0+ 还需要gps权限才可以拿到net name，否则返回<unknow ssid>
                 */
                data.NetName = wifiInfo.getSSID();
                data.IP = Utils.formatIntIpAddress(wifiInfo.getIpAddress());
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        try {
            Log.d("mayqx", "network:" + DataConfig.getJsonConvert().toJson(data));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        return data;
    }
}
