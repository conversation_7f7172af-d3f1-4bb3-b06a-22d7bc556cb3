package com.ppdai.module.datacollection.data;

import android.app.usage.UsageStats;
import android.app.usage.UsageStatsManager;
import android.content.Context;
import android.os.Build;
import android.util.Log;

import com.ppdai.module.datacollection.AcType;
import com.ppdai.module.datacollection.base.BaseDataCollectionTask;
import com.ppdai.module.datacollection.domain.AppUsageInformation;
import com.ppdai.module.datacollection.utils.DataConfig;

import org.json.JSONObject;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.Random;
import java.util.UUID;

/**
 * Created by pony on 2024/9/26
 */
public class AppUsageData extends BaseDataCollectionTask {

    private static final long ONE_DAY = 1000 * 60 * 60 * 24 * 1L;

    /**
     * Preferences保存的应用程序key
     */
    private static final String KEY_APP_USAGE = "key_app_usage_last_time";

    String uuid = UUID.randomUUID().toString();

    public enum Type {
        // 日级别：最长过去7天
        daily(UsageStatsManager.INTERVAL_DAILY, Calendar.DATE, 7),
        // 周级别：最长过去4周
        weekly(UsageStatsManager.INTERVAL_WEEKLY, Calendar.WEEK_OF_YEAR, 4),
        // 月级别：最长过去6月 (考虑到数据量和实效性，目前只采集最近3个月数据)
        monthly(UsageStatsManager.INTERVAL_MONTHLY, Calendar.MONTH, 3);
        // monthly(UsageStatsManager.INTERVAL_MONTHLY, Calendar.MONTH, 6);
        // 年级别：最长过去2年  (@月青，暂时不采集)
        // yearly(UsageStatsManager.INTERVAL_YEARLY, Calendar.YEAR, 2);

        public int intervalType;
        public int calendarField;
        public int max;

        Type(int intervalType, int calendarField, int max) {
            this.intervalType = intervalType;
            this.calendarField = calendarField;
            this.max = max;
        }
    }

    public AppUsageData() {

    }

    public AppUsageData(long intervalTime) {
        this.mIntervalTime = intervalTime;

    }

    @Override
    protected String dataCollection() {
        return getApplicationInfo();
    }

    @Override
    protected String getDataType() {
        return AcType.appUsage.type;
    }

    @Override
    protected long getIntervalTime() {

        return mIntervalTime != 0 ? mIntervalTime : ONE_DAY;
    }


    @Override
    protected String getPreferenceKey() {
        return KEY_APP_USAGE;
    }

    @Override
    protected boolean isAsyncTask() {
        return true;
    }

    @Override
    protected String getUUID() {
        return uuid;
    }

    @Override
    protected String[] getRequestPermission() {
        return new String[0];
    }


    private String getApplicationInfo() {


        List<AppUsageInformation> appUsageInformationList = getAppUsageData();
        if (appUsageInformationList == null || appUsageInformationList.size() == 0) {
            return null;
        }
        try {
            return DataConfig.getJsonConvert().toJson(appUsageInformationList);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public List<AppUsageInformation> getAppUsageData() {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP_MR1) {
            // 5.1以下不支持
            return null;
        }

        Calendar calendar;
        long beginTime;
        long endTime;
        int interval;

        List<AppUsageInformation> list = new ArrayList<>();
        UsageStatsManager usageStatsManager = (UsageStatsManager)
                DataConfig.getApplicationContext().getSystemService(Context.USAGE_STATS_SERVICE);

        for (Type type : Type.values()) {
            calendar = Calendar.getInstance();
            endTime = calendar.getTimeInMillis();
            // 倒退指定时长
            calendar.add(type.calendarField, -type.max);
            beginTime = calendar.getTimeInMillis();
            interval = type.intervalType;

            List<UsageStats> queryUsageStats = usageStatsManager.queryUsageStats(interval, beginTime, endTime);

            /*
            Log.d("lhw", "type: " + type.name()
                    + ", startTime: " + format(beginTime)
                    + ", endTime: " + format(endTime)
                    + ", interval: " + interval
                    + ", item size: " + queryUsageStats.size());
                    */

            for (UsageStats usage : queryUsageStats) {
                AppUsageInformation item = new AppUsageInformation();
                item.IntervalType = interval + "";
                item.PackageName = usage.getPackageName();
                item.LaunchCount = getLaunchCount(usage) + "";
                item.StartTime = usage.getFirstTimeStamp() + "";
                item.EndTime = usage.getLastTimeStamp() + "";
                item.LastTimeUsed = usage.getLastTimeUsed() + "";
                item.TotalTimeUsed = usage.getTotalTimeInForeground() + "";

                // Log.d("lhw", "item: " + new JSONObject(item).toString());
                list.add(item);
            }
        }

        try {
            int random = new Random().nextInt(list.size());
            Log.d("mayqx", "app usage, size=" + list.size() + ", 随机采样:" + DataConfig.getJsonConvert().toJson(list.get(random)));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        return list;
    }

    /**
     * 获取启动次数 （不是很准）
     *
     * @param usage
     */
    private int getLaunchCount(UsageStats usage) {
        // mAppLaunchCount 在浅灰名单中，仍可使用，注意catch异常
        try {
            Field launchCount = usage.getClass().getDeclaredField("mLaunchCount");
            launchCount.setAccessible(true);
            return launchCount.getInt(usage);
        } catch (Throwable e) {
            e.printStackTrace();
        }
        return 0;
    }


}
