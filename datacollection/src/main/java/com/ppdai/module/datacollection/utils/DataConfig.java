package com.ppdai.module.datacollection.utils;

import android.content.Context;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;

import com.ppdai.module.datacollection.JsonConverter;
import com.ppdai.module.datacollection.base.CollectFinishCallback;
import com.ppdai.module.datacollection.base.UploadHandler;

/**
 * Created by <PERSON><PERSON><PERSON> on 2016/3/11.
 */
public class DataConfig {

    public static final String VERSION = "2.0";
    private static Context sContext;
    private static UploadHandler sListener;
    private static JsonConverter sJsonConvert = new SimpleJsonConverter();
    private static CollectFinishCallback sCollectFinishCallback;
    private static String sBusinessType;
    private static boolean sIsCollection;
    private static String sUserId;
    private static boolean sIsGrade;
    private static boolean sIsSIgnatue;
    private static String sDateFormatPattern = "yyyy-MM-dd HH:mm:ss";
    private static String sSpecialVersionName;
    private static int sImageAttrUploadOnceCountLimit = 500;

    /**
     * 收集数据上传的时间的步骤
     */
    public static final String STEP_ONE = "1"; //开始
    public static final String STEP_TWO = "2"; //结束

    public static void initApplicationcontext(Context applicationConetext) {
        sContext = applicationConetext;
    }

    public static Context getApplicationContext() {
        return sContext;
    }

    public static void setUploadhandler(UploadHandler listener) {
        sListener = listener;
    }

    public static UploadHandler getUploadHandler() {
        return sListener;
    }

    public static JsonConverter getJsonConvert() {
        return sJsonConvert;
    }

    public static void setJsonConvert(JsonConverter jsonConvert) {
        DataConfig.sJsonConvert = jsonConvert;
    }

    public static String getDateFormatPattern() {
        return sDateFormatPattern;
    }

    public static void setDateFormatPattern(String dateFormatPattern) {
        sDateFormatPattern = dateFormatPattern;
    }

    public static void setCollectFinishCallback(CollectFinishCallback mCollectFinishCallback) {
        DataConfig.sCollectFinishCallback = mCollectFinishCallback;
    }

    public static CollectFinishCallback getCollectFinishCallback() {
        return sCollectFinishCallback;
    }

    /**
     * 设置userid
     *
     * @param userId
     */
    public static void setUserId(String userId) {
        sUserId = userId;
    }

    public static String getUserId() {
        return sUserId;
    }

    /**
     * 是否需要签名
     *
     * @param signature
     */
    public static void setIsSignature(boolean signature) {
        sIsSIgnatue = signature;
    }

    public static boolean getIsSignature() {
        return sIsSIgnatue;
    }

    /**
     * 设置来源
     *
     * @param businessTpye 未知:0   借入: 1  借出: 2  拍分期 : 3
     */
    public static void setBusinessType(String businessTpye) {
        sBusinessType = businessTpye;
    }

    public static String getBusinessType() {
        return sBusinessType;
    }

    /**
     * 设置是否收集上传时间
     *
     * @param isColleaction 借入true  其他false
     */
    public static void setCollectionUploadTime(boolean isColleaction) {
        sIsCollection = isColleaction;
    }

    public static boolean getColleactionUploadTime() {
        return sIsCollection;
    }

    /**
     * 是否收集打分
     *
     * @param isGrade
     */
    public static void setGrade(boolean isGrade) {
        sIsGrade = isGrade;
    }

    public static boolean getGrade() {
        return sIsGrade;
    }

    public static void setSpecialVersionName(String versionName) {
        sSpecialVersionName = versionName;
    }

    /**
     * 设置图片上传张数限制
     */
    public static void setImageAttrUploadOnceCountLimit(int limit) {
        sImageAttrUploadOnceCountLimit = limit;
    }

    public static int getImageAttrUploadOnceCountLimit() {
        return sImageAttrUploadOnceCountLimit;
    }

    public static String getAppVersionName() {
        if (sSpecialVersionName != null) {
            return sSpecialVersionName;
        }
        try {
            PackageManager packageManager = DataConfig.getApplicationContext().getPackageManager();
            String packageName = DataConfig.getApplicationContext().getPackageName();
            PackageInfo packageInfo = packageManager.getPackageInfo(packageName, 0);
            sSpecialVersionName = packageInfo.versionName;
            return sSpecialVersionName;
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
            return "";
        }
    }

    /**
     * 安卓平台
     */
    public static final int OS_CODE = 2;

    /**
     * 采集数据状态
     */
    public static final String TYPE_DATA_STATUS = "0";


    /**
     * 保存各类信息接口
     */
    public static final String API_DATA_COLLECTION_INFO = "/realtime/mobile/AppAddPhoneRecord";

    /**
     * 监控数据采集上传的时间
     */
    public static final String API_SAVE_APP_DATA_COLLECTION_TIME = "/PPDMobileBorrow/AppDataCollectService/SaveAppDataCollectTime";


}
