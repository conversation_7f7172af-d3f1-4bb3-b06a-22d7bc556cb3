package com.ppdai.module.datacollection.data;

import android.database.Cursor;
import android.media.ExifInterface;
import android.provider.MediaStore.Images.Media;
import android.util.Log;

import com.ppdai.module.datacollection.BuildConfig;
import com.ppdai.module.datacollection.utils.DataConfig;
import com.ppdai.module.datacollection.utils.DataEncoder;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.Locale;
import java.util.Map;
import java.util.TreeMap;
import java.util.concurrent.Callable;
import java.util.concurrent.TimeUnit;

/**
 * 图片属性采集任务。通过给定的 topId, bottomId Limit， 查询 大于TopId 和小于BottomId 的数据，最多采集 Limit 张。
 * 并对采集完成的数据进行 压缩加密，加密算法为 Base64(AES(GZip(... ORIGIN DATA ...))) 实现。
 */
class ImageAttributeTask implements Callable<ImageAttributeTask.ResultHolder<DataEncoder.CryptoResult>> {

    private static final Map<String, String> EXIF_TAG_MAP;

    static {
        Map<String, String> EXIF_TAG_MAP1;
        try {
            EXIF_TAG_MAP1 = readExifTags();
        } catch (IllegalAccessException e) {
            EXIF_TAG_MAP1 = Collections.EMPTY_MAP;
        }
        EXIF_TAG_MAP = EXIF_TAG_MAP1;
    }

    private long mTopId = -1;
    private long mBottomId = -1;
    private int mLimit;

    public ImageAttributeTask(long topId, long bottomId, int limit) {
        this.mTopId = topId;
        this.mBottomId = bottomId;
        this.mLimit = limit;
    }

    @Override
    public ResultHolder<DataEncoder.CryptoResult> call() {
        ResultHolder<JSONArray> resultHolder = queryDataByImageIdRange();
        if (resultHolder.isEmpty()) {
            return ResultHolder.empty();
        }

        try {
            return ResultHolder.success(
                    resultHolder.topId,
                    resultHolder.bottomId,
                    DataEncoder.get().encoder(resultHolder.data.toString()));
        } catch (Exception e) {
            e.printStackTrace();
            return ResultHolder.empty();
        }
    }

    private ResultHolder<JSONArray> queryDataByImageIdRange() {
        JSONArray result = new JSONArray();
        Cursor cursor = loadCursor();
        long topId = -1;
        long bottomId = -1;

        if (cursor == null) {
            return ResultHolder.empty();
        }

        try {
            if (cursor.getCount() == 0) {
                return ResultHolder.empty();
            }

            String path = null;
            JSONObject item;

            if (cursor.moveToFirst()) {
                topId = cursor.getLong(0);
                cursor.moveToPrevious();
            }
            for (; cursor.moveToNext(); ) {
                item = new JSONObject();
                try {
                    path = cursor.getString(1);
                    if (path == null) {
                        continue;
                    }
                    if (BuildConfig.DEBUG) {
                        item.put("photo_id", cursor.getLong(0) + "");
                    }
                    item.put("photo_name", formatName(path));
                    item.put("photo_time", formatDateTime(cursor.getLong(2)));
                    item.put("detail", createItem(path));
                } catch (Exception e) {
                    Log.d("##", "ERROR => " + path);
                    continue;
                }
                result.put(item);
            }
            if (cursor.moveToPrevious()) {
                bottomId = cursor.getLong(0);
            }
            return ResultHolder.success(topId, bottomId, result);
        } finally {
            cursor.close();
        }
    }

    private Cursor loadCursor() {
        final String[] projection = new String[]{
                Media._ID,
                Media.DATA,
                Media.DATE_ADDED,
                Media.MIME_TYPE};

        StringBuilder selectionBuilder = new StringBuilder();
        ArrayList<String> selectionArgsList = new ArrayList<>(3);
        buildSelectionAndArgs(selectionBuilder, selectionArgsList);

        final String[] selectionArgs = new String[selectionArgsList.size()];
        selectionArgsList.toArray(selectionArgs);
        return DataConfig.getApplicationContext().getContentResolver()
                .query(Media.EXTERNAL_CONTENT_URI,
                        projection,
                        selectionBuilder.toString(),
                        selectionArgs,
                        Media._ID + " DESC LIMIT " + mLimit + " OFFSET 0");
    }

    private float[] latLong = new float[2];

    private JSONObject createItem(String path) throws IOException, JSONException {
        ExifInterface exifInterface = new ExifInterface(path);
        JSONObject item = new JSONObject();
        String tag;
        String attrValue;

        // 重置经纬度
        latLong[0] = 0;
        latLong[1] = 0;
        // 直接先取出经纬度
        exifInterface.getLatLong(latLong);
        for (Map.Entry<String, String> entry : EXIF_TAG_MAP.entrySet()) {
            tag = entry.getValue();
            // 对特殊字段做取值
            if (ExifInterface.TAG_GPS_LATITUDE.equals(tag)) {
                attrValue = latLong[0] + "";
            } else if (ExifInterface.TAG_GPS_LONGITUDE.equals(tag)) {
                attrValue = latLong[1] + "";
            } else if (ExifInterface.TAG_GPS_ALTITUDE.equals(tag)) {
                attrValue = exifInterface.getAltitude(0) + "";
            } else {
                // 其他属性
                attrValue = exifInterface.getAttribute(tag);
            }
            if (attrValue != null) {
                item.put(tag, attrValue);
            }
        }
        return item;
    }

    private void buildSelectionAndArgs(StringBuilder outSelection, ArrayList<String> outSelectionArgs) {
        outSelection.append(Media.MIME_TYPE).append(" = ? ");
        outSelectionArgs.add("image/jpeg");

        if (mTopId > 0) {
            outSelection.append(" AND (").append(Media._ID).append(" > ?");
            outSelectionArgs.add(mTopId + "");
        }

        if (mBottomId > 0) {
            outSelection.append(mTopId > 0 ? " OR " : " AND (").append(Media._ID).append(" < ?");
            outSelectionArgs.add(mBottomId + "");
        }

        if (mTopId > 0 || mBottomId > 0) {
            outSelection.append(")");
        }
    }

    private static String formatName(String path) {
        if (path != null) {
            int len = path.length();
            if (len > 50) {
                return path.substring(len - 50, len);
            }
        }
        return path;
    }

    private static final ThreadLocal<DateFormat> DATE_FORMAT = new ThreadLocal<DateFormat>() {
        @Override
        protected DateFormat initialValue() {
            return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());
        }
    };

    private static String formatDateTime(long datetime) {
        return DATE_FORMAT.get().format(new Date(TimeUnit.MILLISECONDS.convert(datetime, TimeUnit.SECONDS)));
    }


    private static TreeMap<String, String> readExifTags() throws IllegalAccessException {
        TreeMap<String, String> tagMap = new TreeMap<>(String.CASE_INSENSITIVE_ORDER);
        Field[] fields = ExifInterface.class.getDeclaredFields();
        int modifiers;
        String fieldName;
        for (Field field : fields) {
            modifiers = field.getModifiers();
            fieldName = field.getName();
            if (Modifier.isPublic(modifiers)
                    && Modifier.isStatic(modifiers)
                    && fieldName.startsWith("TAG_")
                    && !field.isAnnotationPresent(Deprecated.class)) {
                tagMap.put(fieldName, (String) field.get(null));
            }
        }
        return tagMap;
    }


    public static final class ResultHolder<T> {
        public final long topId;
        public final long bottomId;
        public final T data;

        private ResultHolder(long topId, long bottomId, T data) {
            this.topId = topId;
            this.bottomId = bottomId;
            this.data = data;
        }

        public static <T> ResultHolder success(long topId, long bottomId, T data) {
            return new ResultHolder(topId, bottomId, data);
        }

        public static ResultHolder empty() {
            return new ResultHolder(-1, -1, null);
        }

        public boolean isEmpty() {
            return data == null;
        }
    }
}
