package com.ppdai.module.datacollection.data;

import android.Manifest;
import android.content.Context;
import android.location.Location;
import android.location.LocationListener;
import android.location.LocationManager;
import android.os.Build;
import android.os.Bundle;
import android.os.HandlerThread;
import android.os.Looper;
import android.util.Log;

import com.ppdai.module.datacollection.AcType;
import com.ppdai.module.datacollection.base.BaseDataCollectionTask;
import com.ppdai.module.datacollection.domain.LocationInformation;
import com.ppdai.module.datacollection.utils.DataConfig;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.UUID;

/**
 * Created by liwen on 2016/3/14.
 */
public class LocationInfoData extends BaseDataCollectionTask {

    /**
     * Preferences保存的手机位置key
     */
    private static final String KEY_LOCATION = "key_location_last_time";

    private static final long ONE_DAY = 1000 * 60 * 60 * 24L;

    String uuid = UUID.randomUUID().toString();

    public LocationInfoData() {
        mHandlerThreadBuild = new HandlerThreadBuild() {
            @Override
            public HandlerThread build(String str) {
                return new HandlerThread(str);
            }
        };
    }

    public LocationInfoData(long intervalTime) {
        this.mIntervalTime = intervalTime;
    }

    @Override
    protected String dataCollection() {
        return getLocationInfo();
    }

    @Override
    protected String getDataType() {
        return AcType.locationInfo.type;
    }

    @Override
    protected long getIntervalTime() {
        return mIntervalTime != 0 ? mIntervalTime : ONE_DAY;
    }

    @Override
    protected String getPreferenceKey() {
        return KEY_LOCATION;
    }

    @Override
    protected boolean isAsyncTask() {
        return true;
    }

    @Override
    protected String getUUID() {
        return uuid;
    }

    @Override
    protected String[] getRequestPermission() {
        return new String[]{
                Manifest.permission.ACCESS_FINE_LOCATION};
    }

    interface HandlerThreadBuild {
        HandlerThread build(String str);
    }

    private HandlerThreadBuild mHandlerThreadBuild;

    public void setHandlerThreadBuild(HandlerThreadBuild handlerThreadBuild) {
        this.mHandlerThreadBuild = handlerThreadBuild;
    }


    private String getLocationInfo() {
        HandlerThread handlerThread = mHandlerThreadBuild.build("Location-Handler-Thread");
        handlerThread.start();
        Looper mLocationHandlerLooper = handlerThread.getLooper();
        try {
            LocationInformation locationInformation = getLocation(mLocationHandlerLooper);
            if (locationInformation == null) {
                quitLooper(handlerThread);
                return null;
            }
            return DataConfig.getJsonConvert().toJson(locationInformation);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            quitLooper(handlerThread);
        }
        return null;
    }

    private void quitLooper(HandlerThread handlerThread) {
        if (handlerThread == null) {
            return;
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR2) {
            handlerThread.quitSafely();
        } else {
            handlerThread.quit();
        }
    }

    public LocationInformation getLocation(Looper looper) {
        Log.d("mayqx", "sdk开始定位");
        Location bestLocation = null;
        try {
            LocationManager locationManager = (LocationManager) DataConfig.getApplicationContext().getSystemService(Context.LOCATION_SERVICE);
            if (locationManager != null) {
                List<String> providers = locationManager.getProviders(true);
                if (providers != null) {
                    for (String provider : providers) {
                        locationManager.requestLocationUpdates(provider, 1000, 0, locationListener, looper);
                    }

                    // 等定位完成，等太短来不及定位，等太久导致采集慢
                    Thread.sleep(3 * 1000);

                    for (String provider : providers) {
                        Location location = locationManager.getLastKnownLocation(provider);
                        if (location != null
                                && (Math.abs(location.getTime() - System.currentTimeMillis()) < 30 * 1000) //30s内的视为新鲜定位
                                && (bestLocation == null || location.getAccuracy() < bestLocation.getAccuracy())) {
                            Log.d("mayqx", "sdk找到了更优值:" + provider + ", " + DateFormat.getDateTimeInstance().format(new Date(location.getTime())) + ", " + location.getAccuracy() + ", " + location.getLatitude() + ", " + location.getLongitude());
                            bestLocation = location;
                        }
                    }
                }
            }
            locationManager.removeUpdates(locationListener);
        } catch (Exception exception) {
            exception.printStackTrace();
        }
        if (bestLocation != null) {

            try {
                LocationInformation locationInformation = new LocationInformation();
                locationInformation.Latitude = BigDecimal.valueOf(bestLocation.getLatitude()); //纬度
                locationInformation.Longitude = BigDecimal.valueOf(bestLocation.getLongitude());//经度
                locationInformation.Accuracy = BigDecimal.valueOf(bestLocation.getAccuracy());//准确性
                locationInformation.Altitude = BigDecimal.valueOf(bestLocation.getAltitude());//高度
                locationInformation.AltitudeAccuracy = BigDecimal.valueOf(bestLocation.getAccuracy());//高度精确度
                locationInformation.Provider = bestLocation.getProvider(); //来源
                return locationInformation;
            } catch (Exception e) {
                e.printStackTrace();
            }

        } else {
            Log.d("mayqx", "sdk采集位置失败");
        }
        return null;
    }


    LocationListener locationListener = new LocationListener() {

        // Provider的状态在可用、暂时不可用和无服务三个状态直接切换时触发此函数
        @Override
        public void onStatusChanged(String provider, int status, Bundle extras) {

        }

        // Provider被enable时触发此函数，比如GPS被打开
        @Override
        public void onProviderEnabled(String provider) {

        }

        // Provider被disable时触发此函数，比如GPS被关闭
        @Override
        public void onProviderDisabled(String provider) {

        }

        //当坐标改变时触发此函数，如果Provider传进相同的坐标，它就不会被触发
        @Override
        public void onLocationChanged(Location location) {
        }
    };

}
