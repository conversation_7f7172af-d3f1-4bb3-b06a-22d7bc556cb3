package com.ppdai.module.datacollection.utils;

import com.ppdai.module.datacollection.JsonConverter;

import org.json.JSONArray;
import org.json.JSONObject;

import java.lang.reflect.Array;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON>z<PERSON> on 2017/6/12.
 */
public class SimpleJsonConverter implements JsonConverter {
    @Override
    public String toJson(Object obj) throws Exception {
        if (obj instanceof Collection) {
            return toJsonArray((Collection) obj).toString();
        } else if (obj.getClass().isArray()) {
            return toJsonArray(obj).toString();
        } else {
            return toJsonObject(obj).toString();
        }
    }

    private JSONArray toJsonArray(Collection collection) throws Exception {
        JSONArray jsonArray = new JSONArray();
        for (Object obj : collection) {
            jsonArray.put(toJsonObject(obj));
        }
        return jsonArray;
    }

    private JSONArray toJsonArray(Object array) throws Exception {
        List<Object> jsonArray = new ArrayList<Object>();
        for (int i = 0, size = Array.getLength(array); i < size; i++) {
            Object item = Array.get(array, i);
            jsonArray.add(item);
        }
        return toJsonArray(jsonArray);
    }

    private JSONObject toJsonObject(Object obj) {
        if (obj instanceof Map) {
            return new JSONObject((Map) obj);
        }

        JSONObject jsonObject = new JSONObject();

        Class clazz = obj.getClass();
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);
            try {
                jsonObject.put(field.getName(), field.get(obj));
            } catch (Exception e) {
                e.printStackTrace();
                /*try {
                    PrintWriter printWriter = new PrintWriter(new FileOutputStream(new File("xx.log")));
                } catch (FileNotFoundException e1) {
                    e1.printStackTrace();
                }*/
            }
        }
       return jsonObject;
    }
}
