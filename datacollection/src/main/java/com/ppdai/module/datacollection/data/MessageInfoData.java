package com.ppdai.module.datacollection.data;

import android.Manifest;
import android.content.Context;
import android.database.Cursor;
import android.net.Uri;
import android.os.Build;
import android.provider.CallLog;
import android.provider.Telephony;
import androidx.core.content.PermissionChecker;
import android.telephony.TelephonyManager;
import android.text.TextUtils;
import android.util.Log;

import com.ppdai.module.datacollection.AcType;
import com.ppdai.module.datacollection.base.BaseDataCollectionTask;
import com.ppdai.module.datacollection.domain.MessageInformation;
import com.ppdai.module.datacollection.utils.DataConfig;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Random;
import java.util.UUID;

/**
 * Created by liwen on 2016/3/14.
 */
public class MessageInfoData extends BaseDataCollectionTask {

    /**
     * Preferences保存的手机短信key
     */
    private static final String KEY_MESSAGE = "key_message_last_time";

    private static final long ONE_WEEK = 1000 * 60 * 60 * 24 * 7L;

    private static final long ONE_MONTH = 1000 * 60 * 60 * 24 * 30L;


    String uuid = UUID.randomUUID().toString();

    public MessageInfoData() {

    }


    public MessageInfoData(long intervalTime) {
        this.mIntervalTime = intervalTime;
    }

    @Override
    protected String dataCollection() {
        return getMessagesInfo();
    }

    @Override
    protected String getDataType() {
        return AcType.messageRecord.type;
    }


    @Override
    protected long getIntervalTime() {

        return mIntervalTime != 0 ? mIntervalTime : ONE_WEEK;
    }


    @Override
    protected String getPreferenceKey() {
        return KEY_MESSAGE;
    }

    @Override
    protected boolean isAsyncTask() {
        return true;
    }

    @Override
    protected String getUUID() {
        return uuid;
    }

    @Override
    protected String[] getRequestPermission() {
        return new String[]{
                Manifest.permission.READ_SMS
        };
    }


    private String getMessagesInfo() {
        long messageLastTime = getLastRecordUpdateTime();
        String afterTimeString = (messageLastTime == 0 ? null : String.valueOf(messageLastTime));

        if (TextUtils.isEmpty(afterTimeString)) {
            afterTimeString = String.valueOf(System.currentTimeMillis() - ONE_MONTH * 3);
        }
        List<MessageInformation> messageInformationList = getMessages(afterTimeString);
        if (messageInformationList == null || messageInformationList.size() == 0) {
            return null;
        }

        try {
            return DataConfig.getJsonConvert().toJson(messageInformationList);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;


    }

    private Uri getSmsUri() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            return Telephony.Sms.CONTENT_URI;
        }
        return Uri.parse("content://sms");
    }


    public List<MessageInformation> getMessages(String afterTime) {
        Cursor cursor = null;
        try {

            Uri URI_MESSAGE_ALL = getSmsUri();
            String where = "date > ?";
            String[] projection = new String[]{"address", "person", "body", "date", "type"};

            cursor = DataConfig.getApplicationContext().getContentResolver()
                    .query(URI_MESSAGE_ALL, projection, where, new String[]{afterTime}, null);

            if (cursor == null) {
                return null;
            }

            List<MessageInformation> messageInformationList = new ArrayList<>();

            String phoneNumber = getLine1Number();

            while (cursor.moveToNext()) {
                MessageInformation messageInformation = new MessageInformation();

                messageInformation.Number = phoneNumber;
                messageInformation.NumberPeer = cursor.getString(cursor.getColumnIndex("address"));
                messageInformation.Content = cursor.getString(cursor.getColumnIndex("body"));
                messageInformation.Type = cursor.getInt(cursor.getColumnIndex("type"));

                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
                long callDate = cursor.getLong(cursor.getColumnIndex(CallLog.Calls.DATE));
                Date date = new Date(callDate);
                messageInformation.MessageTime = simpleDateFormat.format(date);
                messageInformationList.add(messageInformation);
            }

            try {
                int random = new Random().nextInt(messageInformationList.size());
                Log.d("mayqx", "sms, size=" + messageInformationList.size() + ", 随机采样:" + DataConfig.getJsonConvert().toJson(messageInformationList.get(random)));
            } catch (Exception e) {
                throw new RuntimeException(e);
            }

            return messageInformationList;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }

        return null;
    }


    public String getLine1Number() {
        if (PermissionChecker.checkSelfPermission(DataConfig.getApplicationContext(), Manifest.permission.READ_PHONE_STATE)
                == PermissionChecker.PERMISSION_GRANTED) {
            TelephonyManager telephonyManager = (TelephonyManager) DataConfig.getApplicationContext().getSystemService(Context.TELEPHONY_SERVICE);
            String line1Number = telephonyManager.getLine1Number();
            return TextUtils.isEmpty(line1Number) ? "" : line1Number;
        } else {
            return "";
        }
    }

}
