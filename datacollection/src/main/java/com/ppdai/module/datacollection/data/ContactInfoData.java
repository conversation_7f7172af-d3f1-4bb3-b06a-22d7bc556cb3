package com.ppdai.module.datacollection.data;

import android.Manifest;
import android.content.ContentResolver;
import android.content.Context;
import android.database.Cursor;
import android.net.Uri;
import android.os.Build;
import android.provider.ContactsContract;
import androidx.core.content.PermissionChecker;
import android.telephony.TelephonyManager;
import android.text.TextUtils;
import android.util.Log;

import com.ppdai.module.datacollection.AcType;
import com.ppdai.module.datacollection.base.BaseDataCollectionTask;
import com.ppdai.module.datacollection.domain.ContactInformation;
import com.ppdai.module.datacollection.utils.DataConfig;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;

/**
 * Created by liwen on 2016/3/14.
 */
public class ContactInfoData extends BaseDataCollectionTask {

    private static final long ONE_WEEK = 1000 * 60 * 60 * 24 * 7L;

    /**
     * Preferences保存的联系人key
     */
    private static final String KEY_CONTACT = "key_contact_last_time";


    String uuid = UUID.randomUUID().toString();

    public ContactInfoData() {

    }

    public ContactInfoData(long intervalTime) {
        this.mIntervalTime = intervalTime;
    }

    @Override
    protected String dataCollection() {
        return getContactInfo();
    }

    @Override
    protected String getDataType() {
        return AcType.contacts.type;
    }

    @Override
    protected long getIntervalTime() {
        return mIntervalTime != 0 ? mIntervalTime : ONE_WEEK;
    }

    @Override
    protected String getPreferenceKey() {
        return KEY_CONTACT;
    }

    @Override
    protected boolean isAsyncTask() {
        return true;
    }


    @Override
    protected String getUUID() {
        return uuid;
    }

    @Override
    protected String[] getRequestPermission() {
        return new String[]{Manifest.permission.READ_CONTACTS};
    }

    private String getContactInfo() {

        List<ContactInformation> contactInformationList = queryIncrementContactsByTimeline(
                DataConfig.getApplicationContext().getContentResolver(), getLastRecordUpdateTime());

        if (contactInformationList == null || contactInformationList.size() == 0) {
            Log.d("mayqx", "sdk采集Contact失败，或无联系人");
            return null;
        }

        Log.d("mayqx", "sdk采集Contact成功，size=" + contactInformationList.size() + ", 采样数据=" + contactInformationList.get(0).MobileNumber);

        try {
            return DataConfig.getJsonConvert().toJson(contactInformationList);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private List<ContactInformation> queryIncrementContactsByTimeline(ContentResolver contentResolver, long timestamp) {
        return queryContactsDataByIds(contentResolver,
                queryUpdatedContactsIdByTimeline(contentResolver, timestamp));
    }

    /**
     * 根据时间线查询该时间以来更新的联系人 Id
     *
     * @param contentResolver
     * @param timestamp       查询的基准时间
     * @return 更新的 联系人 Id 数组, 若没有数据，则为 空集合
     */
    private List<Long> queryUpdatedContactsIdByTimeline(ContentResolver contentResolver, long timestamp) {
        Uri contentUri = ContactsContract.Contacts.CONTENT_URI;
        String[] projection = null;
        String selection = null;
        String[] selectionArgs = null;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR2) {
            projection = new String[] {ContactsContract.Data._ID, ContactsContract.Data.CONTACT_LAST_UPDATED_TIMESTAMP};
            selection = String.format("%s >= ?", ContactsContract.Data.CONTACT_LAST_UPDATED_TIMESTAMP);
            selectionArgs = new String[]{timestamp + ""};
        } else {
            projection = new String[] {ContactsContract.Data._ID};
        }

        Cursor cursor = contentResolver.query(
                contentUri,
                projection,
                selection,
                selectionArgs,
                null);

        if (null == cursor) {
            return Collections.emptyList();
        }

        try {
            List<Long> result = new ArrayList<Long>(cursor.getCount());
            final int idIndex = cursor.getColumnIndex(ContactsContract.Data._ID);

            while (cursor.moveToNext()) {
                result.add(cursor.getLong(idIndex));
            }
//            Log.d(">>>>>>>", selection + "  " + timestamp + "\n" + result.toString());
            return result;
        } finally {
            cursor.close();
        }
    }

    private List<ContactInformation> queryContactsDataByIds(ContentResolver contentResolver, List<Long> ids) {
        List<ContactInformation> result = new ArrayList<ContactInformation>(ids.size());
        ContactInformation item;
        for (long id : ids) {
            item = queryContactDataById(contentResolver, id);
            if (item != null) {
                result.add(item);
            }
        }
        return result;
    }

    private ContactInformation queryContactDataById(ContentResolver contentResolver, long contactId) {
        Uri dataUri = buildContactDataUri(contactId);

        String[] projection = new String[] {
                    ContactsContract.Data.MIMETYPE,
                    ContactsContract.Data.DATA1};

        Cursor cursor = contentResolver.query(
                dataUri,
                projection,
                null,
                null,
                null);

        if (cursor == null) {
            return null;
        }

        try {
            ContactInformation result = new ContactInformation();
            result.FixNumber = getLine1Number();

            final int mimeTypeIndex = cursor.getColumnIndex(ContactsContract.Data.MIMETYPE);
            final int data1Index = cursor.getColumnIndex(ContactsContract.Data.DATA1);

            String mimeType;
            String phoneNumber;
            String email;
            String address;
            while (cursor.moveToNext()) {
                mimeType = cursor.getString(mimeTypeIndex);

                switch (mimeType) {
                    case ContactsContract.CommonDataKinds.StructuredName.CONTENT_ITEM_TYPE:
                        result.Name = cursor.getString(data1Index);
//                        Log.d(">>>>", result.Name + "  " + cursor.getLong(2) + "  " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(cursor.getLong(2))));
                        break;

                    case ContactsContract.CommonDataKinds.Phone.CONTENT_ITEM_TYPE:
                        phoneNumber = cursor.getString(data1Index);
                        if (TextUtils.isEmpty(result.MobileNumber)) {
                            result.MobileNumber = phoneNumber;
                        } else if (TextUtils.isEmpty(result.MobileNumberTwo)) {
                            result.MobileNumberTwo = phoneNumber;
                        } else if (TextUtils.isEmpty(result.MobileNumberThree)) {
                            result.MobileNumberThree = phoneNumber;
                        }
                        break;

                    case ContactsContract.CommonDataKinds.Email.CONTENT_ITEM_TYPE:
                        email = cursor.getString(data1Index);
                        if (TextUtils.isEmpty(result.Email)) {
                            result.Email = email;
                        } else if (TextUtils.isEmpty(result.EmailTwo)) {
                            result.EmailTwo = email;
                        } else if (TextUtils.isEmpty(result.EmailThree)) {
                            result.EmailThree = email;
                        }
                        break;

                    case ContactsContract.CommonDataKinds.StructuredPostal.CONTENT_ITEM_TYPE:
                        address = cursor.getString(data1Index);
                        if (TextUtils.isEmpty(result.Address)) {
                            result.Address = address;
                        } else if (TextUtils.isEmpty(result.AddressTwo)) {
                            result.AddressTwo = address;
                        } else if (TextUtils.isEmpty(result.AddressThree)) {
                            result.AddressThree = address;
                        }
                        break;
                }
            }
            return result;
        } finally {
            cursor.close();
        }
    }

    private Uri buildContactDataUri(long contactId) {
        return Uri.parse("content://com.android.contacts/contacts/" + contactId + "/data");
    }

    public String getLine1Number() {
        if (PermissionChecker.checkSelfPermission(DataConfig.getApplicationContext(), Manifest.permission.READ_PHONE_STATE)
                == PermissionChecker.PERMISSION_GRANTED) {
            TelephonyManager telephonyManager = (TelephonyManager) DataConfig.getApplicationContext().getSystemService(Context.TELEPHONY_SERVICE);
            String line1Number = telephonyManager.getLine1Number();
            return TextUtils.isEmpty(line1Number) ? "" : line1Number;
        } else {
            return "";
        }
    }

}
