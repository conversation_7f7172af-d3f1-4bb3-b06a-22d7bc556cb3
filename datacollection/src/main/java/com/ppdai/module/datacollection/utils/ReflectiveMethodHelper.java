package com.ppdai.module.datacollection.utils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import java.util.ArrayList;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/2/6.
 */
public final class ReflectiveMethodHelper {
    private Object target;
    private Class<?> targetClass;
    private ArrayList<Class<?>> parameterTypes;
    private ArrayList<Object> parameters;

    public static ReflectiveMethodHelper of(Object target) {
        if (target == null) {
            throw new IllegalArgumentException();
        }
        return new ReflectiveMethodHelper(target);
    }

    public static ReflectiveMethodHelper of(Class<?> targetClass) {
        if (targetClass == null) {
            throw new IllegalArgumentException();
        }
        return new ReflectiveMethodHelper(targetClass);
    }

    private ReflectiveMethodHelper(@NonNull Object aTarget) {
        this.targetClass = aTarget.getClass();
        this.target = aTarget;
        this.parameterTypes = new ArrayList<Class<?>>(1);
        this.parameters = new ArrayList<Object>(1);
    }

    private ReflectiveMethodHelper(Class<?> aTargetClass) {
        this.targetClass = aTargetClass;
        this.parameterTypes = new ArrayList<Class<?>>(1);
        this.parameters = new ArrayList<Object>(1);
    }

    public ReflectiveMethodHelper parameter(Class<?> parameterType, Object parameter) {
        parameterTypes.add(parameterType);
        parameters.add(parameter);
        return this;
    }

    @Nullable
    public <T> T invoke(String methodName) throws NoSuchMethodException {
        try {
            if (parameterTypes.isEmpty()) {
                return (T) targetClass.getMethod(methodName).invoke(target);
            }
            Class<?>[] parameterTypeArray = new Class[parameterTypes.size()];
            parameterTypes.toArray(parameterTypeArray);
            Object[] parameterArray = new Object[parameters.size()];
            parameters.toArray(parameterArray);
            return (T) targetClass.getMethod(methodName, parameterTypeArray).invoke(target, parameterArray);
        } catch (NoSuchMethodException e) {
            throw e;
        } catch (Exception e) {
            return null;
        }
    }
}
