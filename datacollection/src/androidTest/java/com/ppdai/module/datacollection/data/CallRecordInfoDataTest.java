package com.ppdai.module.datacollection.data;

import android.Manifest;
import android.content.Context;
import android.support.test.InstrumentationRegistry;
import android.support.test.runner.AndroidJUnit4;

import com.ppdai.module.datacollection.AcType;
import com.ppdai.module.datacollection.domain.CallRecordInformation;
import com.ppdai.module.datacollection.utils.DataConfig;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.util.List;

/**
 * Created by fenghao02 on 2017/8/3.
 */
@RunWith(AndroidJUnit4.class)
public class CallRecordInfoDataTest {
    CallRecordInfoData mCallRecordInfoData;
    Context mContext;

    public CallRecordInfoDataTest() {
        super();
    }

    @Before
    public void setUp() throws Exception {
        mCallRecordInfoData = new CallRecordInfoData();
        mContext = InstrumentationRegistry.getContext();
        DataConfig.initApplicationcontext(mContext);
    }

    @After
    public void tearDown() throws Exception {
        mContext = null;
        mCallRecordInfoData = null;
    }

    @Test
    public void dataCollection() throws Exception {
        String str = mCallRecordInfoData.dataCollection();
        Assert.assertNotNull(str);
    }

    @Test
    public void testGetAndSetMethod() throws Exception {
        Assert.assertEquals(AcType.callRecord.type, mCallRecordInfoData.getDataType());
        Assert.assertEquals(1000 * 60 * 60 * 24 * 7L, mCallRecordInfoData.getIntervalTime());
        Assert.assertEquals("key_call_record_last_time", mCallRecordInfoData.getPreferenceKey());
        Assert.assertTrue(mCallRecordInfoData.isAsyncTask());
        Assert.assertNotNull(mCallRecordInfoData.getUUID());
        Assert.assertEquals(Manifest.permission.READ_CALL_LOG, mCallRecordInfoData.getRequestPermission()[0]);
        Assert.assertNotNull(mCallRecordInfoData.getLine1Number());
    }

    @Test
    public void getCallRecords() throws Exception {
        String afterTimeString = String.valueOf(System.currentTimeMillis() - 1000L * 60 * 60 * 24 * 30 * 3);
        List<CallRecordInformation> list = mCallRecordInfoData.getCallRecords(afterTimeString);
        Assert.assertTrue(list.size() > 0);
    }
}