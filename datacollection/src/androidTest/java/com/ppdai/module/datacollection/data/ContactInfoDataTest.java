package com.ppdai.module.datacollection.data;

import android.Manifest;
import android.content.Context;
import android.support.test.InstrumentationRegistry;
import android.support.test.runner.AndroidJUnit4;

import com.ppdai.module.datacollection.AcType;
import com.ppdai.module.datacollection.utils.DataConfig;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

/**
 * Created by fenghao02 on 2017/8/3.
 */
@RunWith(AndroidJUnit4.class)
public class ContactInfoDataTest {

    ContactInfoData mContactInfoData;
    Context mContext;

    @Before
    public void setUp() throws Exception {
        mContactInfoData = new ContactInfoData();
        mContext = InstrumentationRegistry.getContext();
        DataConfig.initApplicationcontext(mContext);
    }

    @After
    public void tearDown() throws Exception {
        mContext = null;
        mContactInfoData = null;
    }

    @Test
    public void testGetAndSetMethod() throws Exception {
        String str = mContactInfoData.dataCollection();
        Assert.assertNotNull(str);

        Assert.assertEquals(AcType.contacts.type, mContactInfoData.getDataType());
        Assert.assertNotNull(mContactInfoData.getIntervalTime());
        Assert.assertEquals("key_contact_last_time", mContactInfoData.getPreferenceKey());
        Assert.assertTrue(mContactInfoData.isAsyncTask());
        Assert.assertNotNull(mContactInfoData.getUUID());
        Assert.assertEquals(Manifest.permission.READ_CONTACTS, mContactInfoData.getRequestPermission()[0]);
        Assert.assertNotNull(mContactInfoData.getLine1Number());
    }
}