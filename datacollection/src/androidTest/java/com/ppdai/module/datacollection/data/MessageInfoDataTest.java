package com.ppdai.module.datacollection.data;

import android.Manifest;
import android.content.Context;
import android.support.test.InstrumentationRegistry;
import android.support.test.runner.AndroidJUnit4;

import com.ppdai.module.datacollection.AcType;
import com.ppdai.module.datacollection.domain.MessageInformation;
import com.ppdai.module.datacollection.utils.DataConfig;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.util.List;


/**
 * Created by fenghao02 on 2017/8/2.
 */
@RunWith(AndroidJUnit4.class)
public class MessageInfoDataTest {
    MessageInfoData messageInfoData;
    Context context;

    @Before
    public void setUp() throws Exception {
        context = InstrumentationRegistry.getContext();
        DataConfig.initApplicationcontext(context);
        messageInfoData = new MessageInfoData();
    }

    @After
    public void tearDown() throws Exception {
        messageInfoData = null;
    }

    @Test
    public void testGetAndSetMethod() throws Exception {
        String str = messageInfoData.dataCollection();
        Assert.assertNotNull(str);

        Assert.assertEquals(AcType.messageRecord.type, messageInfoData.getDataType());
        Assert.assertEquals(1000 * 60 * 60 * 24 * 7L, messageInfoData.getIntervalTime());
        Assert.assertEquals("key_message_last_time", messageInfoData.getPreferenceKey());
        Assert.assertTrue(messageInfoData.isAsyncTask());
        Assert.assertNotNull(messageInfoData.getUUID());
        Assert.assertEquals(Manifest.permission.READ_SMS, messageInfoData.getRequestPermission()[0]);
        Assert.assertNotNull(messageInfoData.getLine1Number());

        String afterTime = String.valueOf(System.currentTimeMillis() - 1000L * 60 * 60 * 24 * 7 * 3);
        List<MessageInformation> list = messageInfoData.getMessages(afterTime);
        Assert.assertTrue(list.size() > 0);
    }
}