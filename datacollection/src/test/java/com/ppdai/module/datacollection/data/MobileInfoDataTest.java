package com.ppdai.module.datacollection.data;

import android.Manifest;
import android.content.Context;
import android.telephony.TelephonyManager;

import com.ppdai.module.datacollection.AcType;
import com.ppdai.module.datacollection.BuildConfig;
import com.ppdai.module.datacollection.utils.DataConfig;
import com.ppdai.module.datacollection.utils.SimpleJsonConverter;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.robolectric.RobolectricTestRunner;
import org.robolectric.Shadows;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowApplication;
import org.robolectric.shadows.ShadowTelephonyManager;

import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.verify;

/**
 * Created by fenghao02 on 2017/8/2.
 */
@RunWith(RobolectricTestRunner.class)
@Config(constants = BuildConfig.class)
public class MobileInfoDataTest {

    MobileInfoData mMobileInfoData;
    Context mShadowApplication;
    @Before
    public void setUp() throws Exception {
        mMobileInfoData = new MobileInfoData();
        mShadowApplication = ShadowApplication.getInstance().getApplicationContext();
        DataConfig.initApplicationcontext(mShadowApplication);
        DataConfig.setJsonConvert(new SimpleJsonConverter());
    }

    @After
    public void tearDown() throws Exception {
        mMobileInfoData = null;
        mShadowApplication = null;
    }

    @Test
    public void dataCollection() throws Exception {
        MobileInfoData spy = spy(mMobileInfoData);
        String s = spy.dataCollection();
        verify(spy).getBrand();
        verify(spy).getModel();
        verify(spy).getIMSI();
        verify(spy).getIMEI();
        verify(spy).getOSVerison();
        verify(spy).getSimOperatorInfo();
        verify(spy).getLine1Number();
        verify(spy).getLine2Number();
        verify(spy).getAppVersionName();
        Assert.assertNotNull(s);
    }

    @Test
    public void getIMEI() throws Exception {
        final ShadowTelephonyManager[] shadowTelephonyManager = new ShadowTelephonyManager[1];
        Context spyContext = spy(mShadowApplication);
        DataConfig.initApplicationcontext(spyContext);
        doAnswer(new Answer() {
            @Override
            public Object answer(InvocationOnMock invocation) throws Throwable {
                TelephonyManager telephonyManager = (TelephonyManager) invocation.callRealMethod();
                shadowTelephonyManager[0] = Shadows.shadowOf(telephonyManager);
                shadowTelephonyManager[0].setDeviceId("123");
                return telephonyManager;
            }
        }).when(spyContext).getSystemService(Context.TELEPHONY_SERVICE);

        MobileInfoData spy = spy(mMobileInfoData);
        String str = spy.getIMEI();
        Assert.assertEquals(str, "123");
    }

    @Test
    public void testGetAndSetMethod() throws Exception {
        Assert.assertEquals(AcType.mobileInfo.type, mMobileInfoData.getDataType());
        Assert.assertEquals(1000 * 60 * 60 * 24L, mMobileInfoData.getIntervalTime());
        Assert.assertEquals("key_device_last_time", mMobileInfoData.getPreferenceKey());
        Assert.assertTrue(mMobileInfoData.isAsyncTask());
        Assert.assertNotNull(mMobileInfoData.getUUID());

        String[] str = mMobileInfoData.getRequestPermission();
        Assert.assertEquals(Manifest.permission.READ_PHONE_STATE, str[0]);

        MobileInfoData spy = spy(mMobileInfoData);
        Assert.assertNotNull(spy.getBrand());

        Assert.assertNotNull(spy.getModel());
        Assert.assertNotNull(spy.getIMSI());
        Assert.assertNotNull(spy.getOSVerison());
        Assert.assertNotNull(spy.getSimOperatorInfo());
        Assert.assertNotNull(spy.getLine1Number());
        Assert.assertNotNull(spy.getLine2Number());

        String str1 = mMobileInfoData.getAppVersionName();
        Assert.assertEquals("1.7", str1);
    }

    //测试中国移动
    @Test
    public void getSimOperatorInfoForYiDong() throws Exception {
        final ShadowTelephonyManager[] shadowTelephonyManager = new ShadowTelephonyManager[1];
        Context spyContext = spy(mShadowApplication);
        DataConfig.initApplicationcontext(spyContext);
        doAnswer(new Answer() {
            @Override
            public Object answer(InvocationOnMock invocation) throws Throwable {
                TelephonyManager telephonyManager = (TelephonyManager) invocation.callRealMethod();
                shadowTelephonyManager[0] = Shadows.shadowOf(telephonyManager);
                shadowTelephonyManager[0].setSimOperator("46000");
                return telephonyManager;
            }
        }).when(spyContext).getSystemService(Context.TELEPHONY_SERVICE);

        MobileInfoData spy = spy(mMobileInfoData);

        String str = spy.getSimOperatorInfo();
        Assert.assertEquals(str, "中国移动");
    }
}