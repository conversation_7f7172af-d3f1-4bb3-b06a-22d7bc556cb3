package com.ppdai.module.datacollection.utils;

import android.content.Context;
import android.content.SharedPreferences;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.robolectric.RobolectricTestRunner;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowApplication;

import static com.ppdai.module.datacollection.utils.PreferencesUtils.PREFERENCE_NAME;

/**
 * Created by fenghao02 on 2017/8/4.
 */
@RunWith(RobolectricTestRunner.class)
@Config(constants = Config.Builder.class)
public class PreferencesUtilsTest {

    Context mContext;

    @Before
    public void setUp() {
        mContext = ShadowApplication.getInstance().getApplicationContext();
    }

    @After
    public void tearDown() {
        mContext = null;
    }


    @Test
    public void putString() throws Exception {
        boolean b = PreferencesUtils.putString(mContext, "abc", "123");
        SharedPreferences sharedPreferences = mContext.getSharedPreferences(PREFERENCE_NAME, Context.MODE_PRIVATE);
        String str = sharedPreferences.getString("abc", "");
        Assert.assertEquals(str, "123");
    }

    @Test
    public void getString() throws Exception {
        SharedPreferences sharedPreferences = mContext.getSharedPreferences(PREFERENCE_NAME, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = sharedPreferences.edit();
        editor.putString("abc", "123");
        editor.commit();

        String str = PreferencesUtils.getString(mContext, "abc");
        Assert.assertEquals(str, "123");
    }


    @Test
    public void putInt() throws Exception {
        PreferencesUtils.putInt(mContext, "key", 123);
        SharedPreferences sharedPreferences = mContext.getSharedPreferences(PREFERENCE_NAME, Context.MODE_PRIVATE);
        int num = sharedPreferences.getInt("key", 0);
        Assert.assertEquals(num, 123);
    }

    @Test
    public void getInt() throws Exception {
        SharedPreferences sharedPreferences = mContext.getSharedPreferences(PREFERENCE_NAME, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = sharedPreferences.edit();
        editor.putInt("key", 123);
        editor.commit();

        int num = PreferencesUtils.getInt(mContext, "key");
        Assert.assertEquals(num, 123);
    }

    @Test
    public void putLong() throws Exception {
        PreferencesUtils.putLong(mContext, "key", 1L);
        SharedPreferences sharedPreferences = mContext.getSharedPreferences(PREFERENCE_NAME, Context.MODE_PRIVATE);
        long num = sharedPreferences.getLong("key", 0L);
        Assert.assertEquals(num, 1L);
    }

    @Test
    public void getLong() throws Exception {
        SharedPreferences sharedPreferences = mContext.getSharedPreferences(PREFERENCE_NAME, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = sharedPreferences.edit();
        editor.putLong("key", 1L);
        editor.commit();

        long num = PreferencesUtils.getLong(mContext, "key");
        Assert.assertEquals(1L, num);
    }


    @Test
    public void putFloat() throws Exception {
        PreferencesUtils.putFloat(mContext, "key", 1f);

        SharedPreferences sharedPreferences = mContext.getSharedPreferences(PREFERENCE_NAME, Context.MODE_PRIVATE);
        float num = sharedPreferences.getFloat("key", 0f);
        Assert.assertEquals(num, 1f, 0.000001);
    }

    @Test
    public void getFloat() throws Exception {
        SharedPreferences sharedPreferences = mContext.getSharedPreferences(PREFERENCE_NAME, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = sharedPreferences.edit();
        editor.putFloat("key", 1f);
        editor.commit();

        float num = PreferencesUtils.getFloat(mContext, "key");
        Assert.assertEquals(num, 1f, 0.00001);
    }

    @Test
    public void putBoolean() throws Exception {
        PreferencesUtils.putBoolean(mContext, "key", true);

        SharedPreferences sharedPreferences = mContext.getSharedPreferences(PREFERENCE_NAME, Context.MODE_PRIVATE);
        boolean b = sharedPreferences.getBoolean("key", false);
        Assert.assertTrue(b);
    }

    @Test
    public void getBoolean() throws Exception {
        SharedPreferences sharedPreferences = mContext.getSharedPreferences(PREFERENCE_NAME, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = sharedPreferences.edit();
        editor.putBoolean("key", true);
        editor.commit();

        boolean key = PreferencesUtils.getBoolean(mContext, "key");
        Assert.assertTrue(key);

    }

}