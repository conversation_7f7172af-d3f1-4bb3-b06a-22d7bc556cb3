package com.ppdai.module.datacollection.data;

import android.Manifest;
import android.content.Context;
import android.location.Criteria;
import android.location.Location;
import android.location.LocationManager;
import android.os.Build;
import android.os.HandlerThread;
import android.os.Looper;

import com.ppdai.module.datacollection.AcType;
import com.ppdai.module.datacollection.domain.LocationInformation;
import com.ppdai.module.datacollection.utils.DataConfig;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.robolectric.RobolectricTestRunner;
import org.robolectric.Shadows;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowApplication;
import org.robolectric.shadows.ShadowLocationManager;
import org.robolectric.shadows.ShadowLooper;

import java.math.BigDecimal;
import java.util.ArrayList;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.verify;

/**
 * Created by fenghao02 on 2017/8/1.
 */
@RunWith(RobolectricTestRunner.class)
@Config(constants = Config.Builder.class)
public class LocationInfoDataTest {
    LocationInfoData mLocationInfoData;
    Context mContext;

    @Before
    public void setUp() throws Exception {
        mLocationInfoData = new LocationInfoData();
        mContext = ShadowApplication.getInstance().getApplicationContext();
        DataConfig.initApplicationcontext(mContext);
    }

    @After
    public void tearDown() throws Exception {
        mLocationInfoData = null;
        mContext = null;
    }


    //测试getLocation 返回null
    @Test
    public void dataCollectionForNull() throws Exception {
        LocationInfoData spy = spy(mLocationInfoData);
        final LocationInformation[] locationInformation = new LocationInformation[1];
        doAnswer(new Answer() {
            @Override
            public Object answer(InvocationOnMock invocation) throws Throwable {
                locationInformation[0] = null;
                return null;
            }
        }).when(spy).getLocation((Looper) any());
        mLocationInfoData.dataCollection();

        Assert.assertNull(locationInformation[0]);
    }

    @Test
    public void dataCollection() throws Exception {
        LocationInfoData spy = spy(mLocationInfoData);

        final HandlerThread handlerThread = new HandlerThread("123");
        final HandlerThread spyHandler = spy(handlerThread);

        LocationInfoData.HandlerThreadBuild handlerThreadBuild = new LocationInfoData.HandlerThreadBuild() {
            @Override
            public HandlerThread build(String str) {
                return spyHandler;
            }
        };

        spy.setHandlerThreadBuild(handlerThreadBuild);

        doAnswer(new Answer() {
            @Override
            public Object answer(InvocationOnMock invocation) throws Throwable {
                LocationInformation locationInformation = new LocationInformation();
                locationInformation.Latitude = BigDecimal.valueOf(123);
                locationInformation.Longitude = BigDecimal.valueOf(321);
                locationInformation.Accuracy = BigDecimal.ONE;
                return locationInformation;
            }
        }).when(spy).getLocation(any(Looper.class));

        String s = spy.dataCollection();
        Assert.assertNotNull(s);

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR2) {
            verify(spyHandler).quitSafely();
        } else {
            verify(spyHandler).quit();
        }

    }

    @Test
    public void testGetAndSetMethod() throws Exception {
        Assert.assertEquals(AcType.locationInfo.type, mLocationInfoData.getDataType());
        Assert.assertEquals(1000 * 60 * 60 * 24L, mLocationInfoData.getIntervalTime());
        Assert.assertEquals("key_location_last_time", mLocationInfoData.getPreferenceKey());
        Assert.assertTrue(mLocationInfoData.isAsyncTask());
        Assert.assertNotNull(mLocationInfoData.getUUID());

        String[] str = mLocationInfoData.getRequestPermission();
        Assert.assertEquals(Manifest.permission.ACCESS_FINE_LOCATION, str[0]);
    }

    @Test
    public void getLocation() throws Exception {
        LocationInfoData spy = spy(mLocationInfoData);
        Context context = spy(mContext);
        DataConfig.initApplicationcontext(context);

        doAnswer(new Answer() {
            @Override
            public Object answer(InvocationOnMock invocation) throws Throwable {
                LocationManager o = (LocationManager) invocation.callRealMethod();
                ShadowLocationManager shadowLocationManager = Shadows.shadowOf(o);
                shadowLocationManager.setProviderEnabled(
                        "gps",
                        true,
                        new ArrayList<Criteria>() {
                            { add(new Criteria()); }
                        });
                Location location = new Location("gps");
                location.setLongitude(123d);
                shadowLocationManager.setLastKnownLocation("gps", location);
                return o;
            }
        }).when(context).getSystemService(Context.LOCATION_SERVICE);

        LocationInformation mation = spy.getLocation(ShadowLooper.getMainLooper());

        ShadowLooper.idleMainLooper();

        Assert.assertEquals(BigDecimal.valueOf(123d), mation.Longitude);
   }
}