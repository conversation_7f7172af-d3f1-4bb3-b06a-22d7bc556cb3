package com.ppdai.module.datacollection;

import android.content.Context;

import com.ppdai.module.datacollection.base.BaseDataCollectionTask;
import com.ppdai.module.datacollection.base.CollectFinishCallback;
import com.ppdai.module.datacollection.base.ResultCallback;
import com.ppdai.module.datacollection.base.UploadHandler;
import com.ppdai.module.datacollection.data.ApplicationInfoData;
import com.ppdai.module.datacollection.data.CallRecordInfoData;
import com.ppdai.module.datacollection.data.ContactInfoData;
import com.ppdai.module.datacollection.data.LocationInfoData;
import com.ppdai.module.datacollection.data.MessageInfoData;
import com.ppdai.module.datacollection.data.MobileInfoData;
import com.ppdai.module.datacollection.test.BuildConfig;
import com.ppdai.module.datacollection.test.shadow.ShadowContactInfoData;
import com.ppdai.module.datacollection.utils.DataConfig;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentMatchers;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.robolectric.RobolectricTestRunner;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowApplication;

import java.util.Map;
import java.util.concurrent.TimeoutException;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Created by fenghao02 on 2017/7/31.
 */
@RunWith(RobolectricTestRunner.class)
@Config(constants = BuildConfig.class,
        shadows = {ShadowContactInfoData.class})
public class DataCollectionTest {

    DataCollection mDataCollection;
    Context mContext;

    @Before
    public void setup() {
        mContext = ShadowApplication.getInstance().getApplicationContext();
        DataConfig.initApplicationcontext(mContext);
        mDataCollection = DataCollection.getInstance();
    }

    @After
    public void tearDown() {
        mDataCollection = null;
    }

    @Test
    public void getInstance() throws Exception {
        DataCollection dataCollection = DataCollection.getInstance();
        Assert.assertEquals(mDataCollection.toString(), dataCollection.toString());
    }

    @Test
    public void collectionRegisterAndLoginData() throws Exception {
        DataCollection spy = spy(mDataCollection);
        final BaseDataCollectionTask mockedTask = mock(BaseDataCollectionTask.class);
        doAnswer(new Answer() {
            @Override
            public Object answer(InvocationOnMock invocation) throws Throwable {
                return mockedTask;
            }
        }).when(spy).mappingTask((AcType) any());
        spy.collectionRegisterAndLoginData();
        verify(mockedTask, times(6)).uploadDataCollection(true);
    }

    @Test
    public void collectionLaunch() throws Exception {
        DataCollection spy = spy(mDataCollection);
        final BaseDataCollectionTask mockedTask = mock(BaseDataCollectionTask.class);
        doAnswer(new Answer() {
            @Override
            public Object answer(InvocationOnMock invocation) throws Throwable {
                return mockedTask;
            }
        }).when(spy).mappingTask((AcType) any());
        spy.collectionLaunch();
        verify(mockedTask, times(6)).uploadDataCollection(false);
    }

    @Test
    public void testMapping() throws Exception{
        Assert.assertEquals(mDataCollection.mappingTask(AcType.applicationInfo).getClass(), new ApplicationInfoData().getClass());
        Assert.assertEquals(mDataCollection.mappingTask(AcType.mobileInfo).getClass(), new MobileInfoData().getClass());
        Assert.assertEquals(mDataCollection.mappingTask(AcType.contacts).getClass(), new ContactInfoData().getClass());
        Assert.assertEquals(mDataCollection.mappingTask(AcType.locationInfo).getClass(), new LocationInfoData().getClass());
        Assert.assertEquals(mDataCollection.mappingTask(AcType.callRecord).getClass(), new CallRecordInfoData().getClass());
        Assert.assertEquals(mDataCollection.mappingTask(AcType.messageRecord).getClass(), new MessageInfoData().getClass());
    }

    @Test(expected = RuntimeException.class)
    public void recordCollectStatusChangedForTypeNotBeExit() throws Exception {
        mDataCollection.recordCollectStatusChanged("xx", true);
    }

    @Test
    public void recordCollectStatusChanged() throws Exception {
        DataCollection spy = spy(mDataCollection);
        UploadHandler uploadHandler = mock(UploadHandler.class);
        DataConfig.setUploadhandler(uploadHandler);
        doAnswer(new Answer() {
            @Override
            public Object answer(InvocationOnMock invocation) throws Throwable {
                Object[] objects = invocation.getArguments();
                ResultCallback resultCallback = (ResultCallback) objects[2];
                resultCallback.uploadSuccess("{\"Result\": 0}");
                return resultCallback;
            }
        }).when(uploadHandler).uploadData((Map)any(), anyString(), (ResultCallback)any());

        doAnswer(new Answer() {
            @Override
            public Object answer(InvocationOnMock invocation) throws Throwable {
                return "1.0";
            }
        }).when(spy).getAppVersionName();

        DataConfig.setGrade(true);
        spy.recordCollectStatusChanged(AcType.mobileInfo.type, true);
        spy.recordCollectStatusChanged(AcType.applicationInfo.type, true);
        spy.recordCollectStatusChanged(AcType.locationInfo.type, true);
        spy.recordCollectStatusChanged(AcType.contacts.type, true);
        spy.recordCollectStatusChanged(AcType.messageRecord.type, true);
        spy.recordCollectStatusChanged(AcType.callRecord.type, true);
    }

    @Test
    public void recordCollectStatusChangedForCallFail() throws Exception {
        DataCollection spy = spy(mDataCollection);
        UploadHandler uploadHandler = mock(UploadHandler.class);
        CollectFinishCallback collectFinishCallback = mock(CollectFinishCallback.class);
        DataConfig.setUploadhandler(uploadHandler);
        DataConfig.setCollectFinishCallback(collectFinishCallback);
        doAnswer(new Answer() {
            @Override
            public Object answer(InvocationOnMock invocation) throws Throwable {
                Object[] objects = invocation.getArguments();
                ResultCallback resultCallback = (ResultCallback) objects[2];
                resultCallback.uploadFail(new TimeoutException());
                return resultCallback;
            }
        }).when(uploadHandler).uploadData(ArgumentMatchers.<String, String>anyMap(), anyString(), ArgumentMatchers.any(ResultCallback.class));

        doAnswer(new Answer() {
            @Override
            public Object answer(InvocationOnMock invocation) throws Throwable {
                return "1.0";
            }
        }).when(spy).getAppVersionName();
        DataConfig.setGrade(true);
        spy.recordCollectStatusChanged(AcType.mobileInfo.type, true);
        spy.recordCollectStatusChanged(AcType.applicationInfo.type, true);
        spy.recordCollectStatusChanged(AcType.locationInfo.type, true);
        spy.recordCollectStatusChanged(AcType.contacts.type, true);
        spy.recordCollectStatusChanged(AcType.messageRecord.type, true);
        spy.recordCollectStatusChanged(AcType.callRecord.type, true);

        verify(collectFinishCallback).onFinish();
    }

    @Test
    public void getAppVersionName() {
        String versionName = mDataCollection.getAppVersionName();
        Assert.assertEquals(BuildConfig.VERSION_NAME, versionName);
    }

    @Test
    public void getSpecialVersionName() {
        DataConfig.setSpecialVersionName("2.0");
        String versionName = mDataCollection.getAppVersionName();
        Assert.assertEquals("2.0", versionName);

        DataConfig.setSpecialVersionName(null);
        versionName = mDataCollection.getAppVersionName();
        Assert.assertEquals(BuildConfig.VERSION_NAME, versionName);
    }

    @Test
    public void packageNameNotFound() {
        Context context = spy(mContext);
        DataConfig.initApplicationcontext(context);
        when(context.getPackageName()).thenReturn("xxx");
        String str = mDataCollection.getAppVersionName();
        Assert.assertEquals(str, "");
    }

}