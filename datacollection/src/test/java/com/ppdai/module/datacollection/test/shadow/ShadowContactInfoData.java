package com.ppdai.module.datacollection.test.shadow;

import com.ppdai.module.datacollection.base.BaseDataCollectionTask;
import com.ppdai.module.datacollection.data.ContactInfoData;

import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;
import org.robolectric.annotation.RealObject;
import org.robolectric.shadow.api.Shadow;
import org.robolectric.util.ReflectionHelpers;

/**
 * Created by fenghao02 on 2017/7/31.
 */

@Implements(ContactInfoData.class)
public class ShadowContactInfoData {

    @RealObject
    BaseDataCollectionTask contact;

    boolean isIgnoreIntervalTime;
    boolean isInvokeUploadDataCollection = false;

    public boolean isIgnoreIntervalTime() {
        return isIgnoreIntervalTime;
    }

    @Implementation
    public void uploadDataCollection(boolean isIgnoreIntervalTime) {
        isInvokeUploadDataCollection = true;
        this.isIgnoreIntervalTime = isIgnoreIntervalTime;

        Shadow.directlyOn(contact,
                BaseDataCollectionTask.class,
                "uploadDataCollection",
                ReflectionHelpers.ClassParameter.from(boolean.class, isIgnoreIntervalTime));
    }

    public boolean isInvokeUploadDataCollection() {
        return isInvokeUploadDataCollection;
    }
}
