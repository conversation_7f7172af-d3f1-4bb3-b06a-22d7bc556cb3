package com.ppdai.module.datacollection.data;

import android.content.Context;

import com.ppdai.module.datacollection.AcType;
import com.ppdai.module.datacollection.BuildConfig;
import com.ppdai.module.datacollection.JsonConverter;
import com.ppdai.module.datacollection.utils.DataConfig;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentMatchers;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.robolectric.RobolectricTestRunner;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowApplication;

import java.util.List;

import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;

/**
 * Created by fenghao02 on 2017/8/1.
 */
@RunWith(RobolectricTestRunner.class)
@Config(constants = BuildConfig.class)
public class ApplicationInfoDataTest {

    ApplicationInfoData mApplicationInfoData;
    Context mShadowApplication;

    @Before
    public void setUp() throws Exception {
        mApplicationInfoData = new ApplicationInfoData();
        mShadowApplication = ShadowApplication.getInstance().getApplicationContext();
        DataConfig.initApplicationcontext(mShadowApplication);
    }

    @After
    public void tearDown() throws Exception {
        mApplicationInfoData = null;
        mShadowApplication = null;
    }

    @Test
    public void dataCollection() throws Exception {
        ApplicationInfoData spy = spy(mApplicationInfoData);
        String str = spy.dataCollection();
        Assert.assertNotNull(str);
    }

    @Test
    public void testSetAndGetMethod() throws Exception {
        Assert.assertEquals(AcType.applicationInfo.type, mApplicationInfoData.getDataType());
        Assert.assertEquals(1000 * 60 * 60 * 24 * 1L, mApplicationInfoData.getIntervalTime());

        ApplicationInfoData applicationInfoData = new ApplicationInfoData(1000L);
        Assert.assertEquals(1000L, applicationInfoData.getIntervalTime());

        Assert.assertEquals("key_application_last_time", mApplicationInfoData.getPreferenceKey());
        Assert.assertTrue(mApplicationInfoData.isAsyncTask());
        Assert.assertNotNull(mApplicationInfoData.getUUID());
        Assert.assertNotNull(mApplicationInfoData.getRequestPermission());

        ApplicationInfoData spy = spy(mApplicationInfoData);
        List list = spy.getApplicationData();
        Assert.assertTrue( (list != null) && (list.size() > 0));
    }

    //测试采集的app信息返回null时的处理
    @Test
    public void getApplicationDataForNull() throws Exception {
        ApplicationInfoData spy = spy(mApplicationInfoData);
        doAnswer(new Answer() {
            @Override
            public Object answer(InvocationOnMock invocation) throws Throwable {
                return null;
            }
        }).when(spy).getApplicationData();

        String str = spy.dataCollection();
        Assert.assertEquals(str, null);
    }

    @Test
    public void getApplicationData() throws Exception {
        ApplicationInfoData spy = spy(mApplicationInfoData);
        List list = spy.getApplicationData();
        Assert.assertTrue( (list != null) && (list.size() > 0));
    }

    //测试object转json 失败抛出异常
    @Test
    public void testObjectToJsonExcepiton() throws Exception {
        JsonConverter jsonConverter = mock(JsonConverter.class);
        DataConfig.setJsonConvert(jsonConverter);
        when(jsonConverter.toJson(ArgumentMatchers.any())).thenThrow(new Exception());

        String str = mApplicationInfoData.dataCollection();
        Assert.assertNull(str);
    }

}