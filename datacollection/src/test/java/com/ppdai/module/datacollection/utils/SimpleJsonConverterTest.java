package com.ppdai.module.datacollection.utils;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

import java.util.ArrayList;

/**
 * Created by fenghao02 on 2017/8/4.
 */
public class SimpleJsonConverterTest {

    SimpleJsonConverter mSimpleJsonConverter;

    @Before
    public void setUp() throws Exception {
        mSimpleJsonConverter = new SimpleJsonConverter();
    }

    @After
    public void tearDown() throws Exception {
        mSimpleJsonConverter = null;
    }

    @Test
    public void toJson() throws Exception {
        SimpleObject simpleObject = new SimpleObject();
        simpleObject.setKey1("value1");
        simpleObject.setKey2("value2");
        String str = mSimpleJsonConverter.toJson(simpleObject);
        Assert.assertEquals(str, "{\"key1\":\"value1\",\"key2\":\"value2\"}");
    }

    //arraylist to json
    @Test
    public void toJsonForCollection() throws Exception {
        SimpleObject simpleObject = new SimpleObject();
        simpleObject.setKey1("value1");
        simpleObject.setKey2("value2");
        SimpleObject simpleObject1 = new SimpleObject();
        simpleObject1.setKey1("value3");
        simpleObject1.setKey2("value4");
        ArrayList<SimpleObject> list = new ArrayList<>();
        list.add(simpleObject);
        list.add(simpleObject1);
        String str = mSimpleJsonConverter.toJson(list);
        Assert.assertEquals(str, "[{\"key1\":\"value1\",\"key2\":\"value2\"},{\"key1\":\"value3\",\"key2\":\"value4\"}]");
    }

    // array to json
    @Test
    public void toJsonForArray() throws Exception {
        SimpleObject[] simpleObjects = new SimpleObject[2];
        SimpleObject simpleObject = new SimpleObject();
        simpleObject.setKey1("value1");
        simpleObject.setKey2("value2");
        SimpleObject simpleObject1 = new SimpleObject();
        simpleObject1.setKey1("value3");
        simpleObject1.setKey2("value4");
        simpleObjects[0] = simpleObject;
        simpleObjects[1] = simpleObject1;
        String str = mSimpleJsonConverter.toJson(simpleObjects);
        Assert.assertEquals(
                "[{\"key1\":\"value1\",\"key2\":\"value2\"},{\"key1\":\"value3\",\"key2\":\"value4\"}]",
                str);
    }

    static class SimpleObject {
        private String key1;

        private String key2;

        public String getKey1() {
            return key1;
        }

        public void setKey1(String key1) {
            this.key1 = key1;
        }

        public String getKey2() {
            return key2;
        }

        public void setKey2(String key2) {
            this.key2 = key2;
        }
    }
}