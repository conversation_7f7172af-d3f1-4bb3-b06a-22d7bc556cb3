# Add project specific ProGuard rules here.
# By default, the flags in this file are appended to flags specified
# in C:\Users\<USER>\AppData\Local\Android\sdk/tools/proguard/proguard-android.txt
# You can edit the include path and order by changing the proguardFiles
# directive in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# Add any project specific keep options here:

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}
-keepattributes Signature
-keepattributes Exceptions

-keep class com.ppdai.module.datacollection.DataCollection { public <methods>; }
-keep class com.ppdai.module.datacollection.JsonConverter { *; }
-keep class com.ppdai.module.datacollection.base.UploadHandler { *; }
-keep class com.ppdai.module.datacollection.base.ResultCallback { *; }
-keep class com.ppdai.module.datacollection.base.CollectFinishCallback { *; }
-keep class com.ppdai.module.datacollection.utils.DataConfig {
    public static <methods>;
    public static final java.lang.String VERSION;
}
-keep class com.ppdai.module.datacollection.domain.** {*;}
-keep class com.ppdai.module.datacollection.AcType {*;}
